# 开发环境配置文件
# 复制此文件为 .env 并根据需要修改配置

# ============================================================================
# 开发模式配置
# ============================================================================

# 开发模式开关 (true/false)
DEV_MODE=true

# 模拟登录验证 (true/false)
# 设置为 true 时，所有登录验证都会返回固定的测试用户
MOCK_LOGIN_VERIFICATION=true

# 跳过真实API调用 (true/false)
# 设置为 true 时，跳过对阿里云服务的真实调用
SKIP_REAL_API_CALLS=true

# 开发模式日志级别
DEV_LOG_LEVEL=DEBUG

# ============================================================================
# 固定测试用户配置
# ============================================================================

# 测试用户的阿里云UID
TEST_USER_ALI_UID=****************

# 测试用户的无影ID
TEST_USER_WY_ID=e269aacf7eaf2eed

# 测试用户的终端用户ID
TEST_USER_END_USER_ID=test_user

# 测试用户的账户类型
TEST_USER_ACCOUNT_TYPE=ALIYUN

# 测试用户的登录类型
TEST_USER_LOGIN_TYPE=PASSWORD

# 测试用户的API密钥ID
TEST_USER_API_KEY_ID=test_api_key

# ============================================================================
# 阿里云配置（开发模式下可以使用假值）
# ============================================================================

# 阿里云Access Key ID（开发模式下可以是假值）
ALIBABA_CLOUD_ACCESS_KEY_ID=fake_access_key_id

# 阿里云Access Key Secret（开发模式下可以是假值）
ALIBABA_CLOUD_ACCESS_KEY_SECRET=fake_access_key_secret

# 阿里云服务端点
ALIBABA_CLOUD_ENDPOINT=wuyingaiinner-pre.aliyuncs.com

# ============================================================================
# 数据库配置
# ============================================================================

# 数据库连接字符串
DATABASE_URL=sqlite:///./dev_database.db

# 数据库连接池大小
DATABASE_POOL_SIZE=10

# ============================================================================
# 服务配置
# ============================================================================

# 服务端口
PORT=8000

# 服务主机
HOST=0.0.0.0

# 调试模式
DEBUG=true

# 日志级别
LOG_LEVEL=DEBUG

# ============================================================================
# 使用说明
# ============================================================================

# 1. 启用开发模式：
#    export DEV_MODE=true
#    export MOCK_LOGIN_VERIFICATION=true

# 2. 禁用开发模式（生产环境）：
#    export DEV_MODE=false
#    export MOCK_LOGIN_VERIFICATION=false
#    # 并设置真实的阿里云凭证

# 3. 测试API调用：
#    curl 'http://localhost:8000/api/sessions/list?loginToken=any_value&regionId=cn-hangzhou'
#    # 在开发模式下，任何 loginToken 都会返回固定的测试用户

# 4. 查看开发配置：
#    curl 'http://localhost:8000/api/dev/config'
