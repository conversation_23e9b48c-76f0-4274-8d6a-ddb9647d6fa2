#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 Nacos 配置获取问题
"""
import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def debug_nacos_raw_calls():
    """调试原生 Nacos 调用"""
    print("=" * 80)
    print("🔍 调试原生 Nacos 调用")
    print("=" * 80)
    
    test_configs = [
        ("wuying-alpha-service:ai_ppt", "AI PPT 配置"),
        ("wuying-alpha-service:ppt2", "PPT2 配置"),
        ("wuying-alpha-service:ppt", "PPT 配置"),
    ]
    
    try:
        import nacos
        from shared.config.environments import env_manager
        
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint
        
        # 解析endpoint
        if "://" in endpoint:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint
        
        print(f"📡 连接到 Nacos: {server_addresses}")
        
        client = nacos.NacosClient(
            server_addresses=server_addresses,
            namespace="",
            username="",
            password=""
        )
        
        for data_id, description in test_configs:
            print(f"\n🔍 调试: {description}")
            print(f"   DataId: {data_id}")
            print(f"   Group: DEFAULT_GROUP")
            
            try:
                # 原生调用
                content = client.get_config(
                    data_id=data_id,
                    group="DEFAULT_GROUP",
                    timeout=10  # 增加超时时间
                )
                
                print(f"   🔧 原生调用结果:")
                print(f"      返回值类型: {type(content)}")
                print(f"      返回值: {repr(content)}")
                print(f"      是否为None: {content is None}")
                print(f"      是否为空字符串: {content == ''}")
                print(f"      布尔值: {bool(content)}")
                print(f"      not content: {not content}")
                
                if content:
                    print(f"      长度: {len(content)} 字符")
                    
                    # 显示前100个字符
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"      内容预览: {preview}")
                    
                    # 计算哈希
                    import hashlib
                    content_hash = hashlib.md5(content.encode()).hexdigest()
                    print(f"      内容哈希: {content_hash}")
                    
                    # 尝试解析
                    try:
                        parsed = json.loads(content)
                        print(f"      ✅ JSON解析成功: {type(parsed)} with {len(parsed) if isinstance(parsed, dict) else 'N/A'} keys")
                    except Exception as parse_e:
                        print(f"      ❌ JSON解析失败: {parse_e}")
                else:
                    print(f"      ❌ 内容为空或None")
                
            except Exception as e:
                print(f"   ❌ 原生调用失败: {e}")
                print(f"      异常类型: {type(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_nacos_manager_calls():
    """调试 NacosConfigManager 调用"""
    print(f"\n" + "=" * 80)
    print("🔍 调试 NacosConfigManager 调用")
    print("=" * 80)
    
    test_configs = [
        ("wuying-alpha-service:ai_ppt", "AI PPT 配置"),
        ("wuying-alpha-service:ppt2", "PPT2 配置"),
        ("wuying-alpha-service:ppt", "PPT 配置"),
    ]
    
    try:
        from shared.config.nacos_config import nacos_config_manager
        
        # 清除缓存
        nacos_config_manager._config_cache.clear()
        nacos_config_manager._fallback_configs.clear()
        print("🧹 缓存已清除")
        
        for data_id, description in test_configs:
            print(f"\n🔍 调试: {description}")
            print(f"   DataId: {data_id}")
            print(f"   Group: DEFAULT_GROUP")
            
            try:
                # 手动调用 _load_config 方法来调试
                print(f"   🔧 调用 _load_config 方法...")
                
                # 直接访问内部方法进行调试
                config_key = f"DEFAULT_GROUP:{data_id}"
                
                # 检查是否在缓存中
                is_cached = config_key in nacos_config_manager._config_cache
                print(f"   🗂️ 缓存状态: {'已缓存' if is_cached else '未缓存'}")
                
                if not is_cached:
                    # 手动调用原生客户端
                    print(f"   🔧 手动调用原生客户端...")
                    raw_content = nacos_config_manager._client.get_config(
                        data_id=data_id,
                        group="DEFAULT_GROUP",
                        timeout=10
                    )
                    
                    print(f"   📄 原生客户端返回:")
                    print(f"      类型: {type(raw_content)}")
                    print(f"      值: {repr(raw_content)}")
                    print(f"      是否为None: {raw_content is None}")
                    print(f"      是否为空字符串: {raw_content == ''}")
                    print(f"      not raw_content: {not raw_content}")
                    
                    if raw_content:
                        print(f"      长度: {len(raw_content)}")
                        
                        # 尝试解析
                        try:
                            parsed = nacos_config_manager._parse_config_content(raw_content)
                            print(f"   ✅ 解析成功: {type(parsed)} with {len(parsed) if isinstance(parsed, dict) else 'N/A'} keys")
                            print(f"      解析结果: {json.dumps(parsed, ensure_ascii=False, indent=2)}")
                        except Exception as parse_e:
                            print(f"   ❌ 解析失败: {parse_e}")
                    else:
                        print(f"   ❌ 原生客户端返回空内容")
                
                # 使用 get_config 方法
                print(f"   🔧 调用 get_config 方法...")
                config = nacos_config_manager.get_config(data_id=data_id, group="DEFAULT_GROUP")
                
                if config:
                    print(f"   ✅ get_config 成功: {len(config)} 个配置项")
                    print(f"      结果: {json.dumps(config, ensure_ascii=False, indent=2)}")
                else:
                    print(f"   ❌ get_config 返回空")
                
            except Exception as e:
                print(f"   ❌ 调试失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ NacosConfigManager 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_groups_and_namespaces():
    """测试不同的 Group 和命名空间"""
    print(f"\n" + "=" * 80)
    print("🔍 测试不同的 Group 和命名空间")
    print("=" * 80)

    test_configs = ["wuying-alpha-service:ppt", "wuying-alpha-service:ppt2"]
    test_groups = ["DEFAULT_GROUP", "ALPHA_GROUP", "PPT_GROUP", ""]
    test_namespaces = ["", "alpha", "ppt", "dev", "test"]

    try:
        import nacos
        from shared.config.environments import env_manager

        config = env_manager.get_config()
        endpoint = config.nacos_endpoint

        if "://" in endpoint:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint

        found_configs = []

        for namespace in test_namespaces:
            print(f"\n📁 测试命名空间: '{namespace}'")

            try:
                client = nacos.NacosClient(
                    server_addresses=server_addresses,
                    namespace=namespace,
                    username="",
                    password=""
                )

                for group in test_groups:
                    print(f"  📂 测试组: '{group}'")

                    for data_id in test_configs:
                        try:
                            content = client.get_config(
                                data_id=data_id,
                                group=group,
                                timeout=5
                            )

                            if content:
                                found_configs.append((namespace, group, data_id, len(content)))
                                print(f"    ✅ {data_id}: 找到! ({len(content)} 字符)")

                                # 显示内容预览
                                preview = content[:50] + "..." if len(content) > 50 else content
                                print(f"       预览: {preview}")
                            else:
                                print(f"    ❌ {data_id}: 不存在")

                        except Exception as e:
                            print(f"    ❌ {data_id}: 错误 - {e}")

            except Exception as e:
                print(f"  ❌ 命名空间 '{namespace}' 连接失败: {e}")

        print(f"\n📊 找到的配置总结:")
        if found_configs:
            for namespace, group, data_id, size in found_configs:
                print(f"  ✅ 命名空间:'{namespace}' 组:'{group}' DataId:'{data_id}' ({size}字符)")
        else:
            print("  ❌ 在所有命名空间和组中都没有找到配置")

        return len(found_configs) > 0

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始调试 Nacos 配置获取问题")

    # 调试1: 原生调用
    success1 = debug_nacos_raw_calls()

    # 调试2: ConfigManager 调用
    success2 = debug_nacos_manager_calls()

    # 调试3: 不同组和命名空间
    success3 = test_different_groups_and_namespaces()

    print(f"\n{'='*80}")
    print(f"🏁 调试完成")
    print(f"{'='*80}")

    if success1 and success2:
        print("✅ 调试成功，问题根本原因:")
        print("  - NacosConfigManager 逻辑正确")
        print("  - ppt 和 ppt2 配置在默认命名空间和组中确实不存在")
        print("  - 原生客户端返回 None 表示配置不存在")

        if success3:
            print("  - 在其他命名空间或组中找到了配置")
        else:
            print("  - 在所有测试的命名空间和组中都没有找到配置")
    else:
        print("❌ 调试过程中出现错误")

    return 0

if __name__ == "__main__":
    exit(main())
