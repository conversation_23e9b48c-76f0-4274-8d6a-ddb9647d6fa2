#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FileService RAG状态查询功能单元测试
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.services.file_service import FileService
from src.domain.services.auth_service import AuthContext
from src.infrastructure.database.models.file_models import AlphaFile, UploadStatus


class TestFileServiceRagStatus(unittest.TestCase):
    """FileService RAG状态查询功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.file_service = FileService()
        self.auth_context = AuthContext(
            user_key="test_user",
            ali_uid=123456,
            wy_id="test_wy_id"
        )
        
    def create_mock_file(self, file_id: int, upload_status: str, **kwargs) -> Mock:
        """创建模拟文件对象"""
        mock_file = Mock(spec=AlphaFile)
        mock_file.id = file_id
        mock_file.ali_uid = self.auth_context.ali_uid
        mock_file.wy_id = self.auth_context.wy_id
        mock_file.upload_status = upload_status
        mock_file.title = kwargs.get('title', 'test_file.pdf')
        mock_file.upload_progress = kwargs.get('upload_progress', 0)
        mock_file.doc_id = kwargs.get('doc_id', None)
        mock_file.error_message = kwargs.get('error_message', None)
        mock_file.gmt_created = kwargs.get('gmt_created', datetime.now())
        mock_file.gmt_modified = kwargs.get('gmt_modified', datetime.now())
        return mock_file
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_analyzing(self, mock_repo):
        """测试获取正在分析中的文件状态"""
        # 准备测试数据
        file_id = 1
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.ANALYZING.value,
            upload_progress=50
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['file_id'], str(file_id))
        self.assertEqual(result['rag_status'], 'analyzing')
        self.assertEqual(result['upload_status'], UploadStatus.ANALYZING.value)
        self.assertEqual(result['progress'], 50)
        self.assertIsNone(result['doc_id'])
        self.assertIsNone(result['error_message'])
        
        # 验证调用
        mock_repo.get_file_by_id.assert_called_once_with(file_id)
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_completed(self, mock_repo):
        """测试获取已完成的文件状态"""
        # 准备测试数据
        file_id = 2
        doc_id = "doc_12345"
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.COMPLETED.value,
            upload_progress=100,
            doc_id=doc_id
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['file_id'], str(file_id))
        self.assertEqual(result['rag_status'], 'completed')
        self.assertEqual(result['upload_status'], UploadStatus.COMPLETED.value)
        self.assertEqual(result['progress'], 100)
        self.assertEqual(result['doc_id'], doc_id)
        self.assertIsNotNone(result['completed_at'])
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_failed(self, mock_repo):
        """测试获取失败的文件状态"""
        # 准备测试数据
        file_id = 3
        error_message = "RAG解析失败"
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.FAILED.value,
            error_message=error_message
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['file_id'], str(file_id))
        self.assertEqual(result['rag_status'], 'failed')
        self.assertEqual(result['upload_status'], UploadStatus.FAILED.value)
        self.assertEqual(result['error_message'], error_message)
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_file_not_found(self, mock_repo):
        """测试文件不存在的情况"""
        # 准备测试数据
        file_id = 999
        mock_repo.get_file_by_id.return_value = None
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNone(result)
        mock_repo.get_file_by_id.assert_called_once_with(file_id)
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_permission_denied(self, mock_repo):
        """测试无权限访问文件的情况"""
        # 准备测试数据
        file_id = 4
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.ANALYZING.value
        )
        # 设置不同的用户ID，模拟无权限
        mock_file.ali_uid = 999999
        mock_file.wy_id = "other_user"
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNone(result)
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_get_rag_status_uploading(self, mock_repo):
        """测试正在上传中的文件状态"""
        # 准备测试数据
        file_id = 5
        mock_file = self.create_mock_file(
            file_id=file_id,
            upload_status=UploadStatus.UPLOADING.value,
            upload_progress=30
        )
        mock_repo.get_file_by_id.return_value = mock_file
        
        # 执行测试
        result = await self.file_service.get_rag_status(self.auth_context, file_id)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['file_id'], str(file_id))
        self.assertEqual(result['rag_status'], 'waiting')  # 等待上传完成
        self.assertEqual(result['upload_status'], UploadStatus.UPLOADING.value)
        self.assertEqual(result['progress'], 30)
    
    def test_async_rag_processing_worker_setup(self):
        """测试异步RAG处理工作线程的设置"""
        # 验证线程池已正确初始化
        self.assertIsNotNone(self.file_service._thread_pool)
        self.assertEqual(self.file_service._thread_pool._max_workers, 5)
        self.assertTrue(self.file_service._thread_pool._thread_name_prefix.startswith("FileService"))
    
    @patch('src.domain.services.file_service.file_repository')
    def test_handle_rag_task_completion_success(self, mock_repo):
        """测试RAG任务成功完成的回调处理"""
        # 创建模拟的Future对象
        mock_future = Mock()
        mock_future.exception.return_value = None  # 无异常表示成功
        
        file_id = 1
        
        # 执行测试
        self.file_service._handle_rag_task_completion(mock_future, file_id)
        
        # 验证没有调用失败标记方法
        mock_repo.mark_file_failed.assert_not_called()
    
    @patch('src.domain.services.file_service.file_repository')
    def test_handle_rag_task_completion_failure(self, mock_repo):
        """测试RAG任务失败的回调处理"""
        # 创建模拟的Future对象
        mock_future = Mock()
        test_exception = Exception("RAG处理异常")
        mock_future.exception.return_value = test_exception
        
        file_id = 1
        
        # 执行测试
        self.file_service._handle_rag_task_completion(mock_future, file_id)
        
        # 验证调用了失败标记方法
        mock_repo.mark_file_failed.assert_called_once_with(
            file_id, f"RAG任务异常: {str(test_exception)}"
        )


class TestAsyncRagProcessing(unittest.TestCase):
    """异步RAG处理功能测试"""
    
    def setUp(self):
        """测试前准备"""
        self.file_service = FileService()
        self.auth_context = AuthContext(
            user_key="test_user",
            ali_uid=123456,
            wy_id="test_wy_id"
        )
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_start_async_rag_processing_session_file(self, mock_repo):
        """测试启动会话文件的异步RAG处理"""
        # 准备测试数据
        mock_file = Mock()
        mock_file.id = 1
        mock_file.type = "sessionFile"
        
        mock_repo.mark_file_analyzing.return_value = True
        
        # 模拟线程池
        with patch.object(self.file_service, '_thread_pool') as mock_pool:
            mock_pool._shutdown = False
            mock_future = Mock()
            mock_pool.submit.return_value = mock_future
            
            # 执行测试
            await self.file_service._start_async_rag_processing(mock_file, self.auth_context)
            
            # 验证调用
            mock_repo.mark_file_analyzing.assert_called_once_with(mock_file.id)
            mock_pool.submit.assert_called_once()
            mock_future.add_done_callback.assert_called_once()
    
    @patch('src.domain.services.file_service.file_repository')
    async def test_start_async_rag_processing_non_session_file(self, mock_repo):
        """测试启动非会话文件的异步RAG处理"""
        # 准备测试数据
        mock_file = Mock()
        mock_file.id = 1
        mock_file.type = "artifactFile"  # 非会话文件
        
        # 执行测试
        await self.file_service._start_async_rag_processing(mock_file, self.auth_context)
        
        # 验证不会调用RAG处理相关方法
        mock_repo.mark_file_analyzing.assert_not_called()


def run_tests():
    """运行所有测试"""
    print("=" * 60)
    print("FileService RAG状态查询功能单元测试")
    print("=" * 60)
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestFileServiceRagStatus))
    suite.addTests(loader.loadTestsFromTestCase(TestAsyncRagProcessing))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ 所有测试通过")
    else:
        print(f"✗ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    print("=" * 60)
    
    return result.wasSuccessful()


async def run_async_tests():
    """运行异步测试"""
    test_instance = TestFileServiceRagStatus()
    test_instance.setUp()
    
    print("运行异步测试...")
    
    # 测试各种状态
    await test_instance.test_get_rag_status_analyzing()
    print("✓ 测试分析中状态通过")
    
    await test_instance.test_get_rag_status_completed()
    print("✓ 测试完成状态通过")
    
    await test_instance.test_get_rag_status_failed()
    print("✓ 测试失败状态通过")
    
    await test_instance.test_get_rag_status_file_not_found()
    print("✓ 测试文件不存在通过")
    
    await test_instance.test_get_rag_status_permission_denied()
    print("✓ 测试权限检查通过")


if __name__ == "__main__":
    # 运行同步测试
    success = run_tests()
    
    # 运行异步测试
    print("\n运行异步测试...")
    asyncio.run(run_async_tests())
    print("✓ 异步测试完成")
