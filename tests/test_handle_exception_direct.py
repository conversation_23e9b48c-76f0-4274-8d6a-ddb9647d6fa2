#!/usr/bin/env python3
"""
直接测试 handle_exception 函数和修改后的逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_handle_exception_function():
    """测试 handle_exception 函数"""
    print("🧪 测试 handle_exception 函数")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from src.presentation.api.dependencies.api_common_utils import handle_exception
        from src.domain.services.session_service import FileContentTooLargeError
        
        # 创建文件内容过大异常
        error = FileContentTooLargeError(
            message="文件内容过多，请精简一下",
            total_length=300000,
            limit=200000
        )
        
        # 调用 handle_exception 函数
        result = handle_exception(error, "test_request_123")
        
        print("✅ handle_exception 函数调用成功:")
        print(f"   返回类型: {type(result)}")
        print(f"   返回内容: {result}")
        
        # 验证返回格式
        expected_fields = ['code', 'message', 'success', 'request_id', 'data', 'status']
        actual_fields = list(result.keys())
        missing_fields = [field for field in expected_fields if field not in result]
        extra_fields = [field for field in actual_fields if field not in expected_fields]
        
        print(f"\n📋 字段检查:")
        print(f"   期望字段: {expected_fields}")
        print(f"   实际字段: {actual_fields}")
        
        if not missing_fields:
            print("   ✅ 所有必需字段都存在")
        else:
            print(f"   ❌ 缺少字段: {missing_fields}")
            
        if not extra_fields:
            print("   ✅ 没有多余字段")
        else:
            print(f"   ℹ️ 额外字段: {extra_fields}")
        
        # 验证具体值
        print(f"\n🔍 值检查:")
        checks = [
            (result.get('code') == 'FILE_CONTENT_TOO_LARGE', f"错误码: {result.get('code')} (期望: FILE_CONTENT_TOO_LARGE)"),
            (result.get('message') == '文件内容过多，请精简一下', f"错误消息: {result.get('message')} (期望: 文件内容过多，请精简一下)"),
            (result.get('success') is False, f"成功标志: {result.get('success')} (期望: False)"),
            (result.get('request_id') == 'test_request_123', f"请求ID: {result.get('request_id')} (期望: test_request_123)"),
            (result.get('status') == 400, f"HTTP状态码: {result.get('status')} (期望: 400)")
        ]
        
        for check, desc in checks:
            if check:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
        
        # 测试其他异常类型
        print(f"\n🧪 测试其他异常类型:")
        
        # 测试普通异常
        normal_error = Exception("普通异常测试")
        normal_result = handle_exception(normal_error, "test_request_456")
        
        print(f"   普通异常处理结果:")
        print(f"   - 错误码: {normal_result.get('code')}")
        print(f"   - 错误消息: {normal_result.get('message')}")
        print(f"   - 成功标志: {normal_result.get('success')}")
        
        return True
        
    except Exception as e:
        print(f"❌ handle_exception 函数测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_code_modification():
    """验证代码修改"""
    print("\n🔍 验证代码修改")
    print("=" * 50)
    
    try:
        # 读取修改后的文件内容
        file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'routes', 'session_routes.py')
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改是否存在
        checks = [
            ('return handle_exception(resource_error, request_id)' in content, "包含直接返回 handle_exception 的代码"),
            ('# 直接返回handle_exception的结果，不抛出异常' in content, "包含修改说明注释"),
            ('logger.warning(f"[API] 资源检查失败: session_id={session_id}, error={resource_error}")' in content, "包含警告日志")
        ]
        
        print("📝 代码修改检查:")
        for check, desc in checks:
            if check:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
        
        # 查找修改的具体位置
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if 'return handle_exception(resource_error, request_id)' in line:
                print(f"\n📍 找到修改位置 (第{i}行):")
                # 显示上下文
                start = max(0, i-5)
                end = min(len(lines), i+3)
                for j in range(start, end):
                    marker = ">>> " if j == i-1 else "    "
                    print(f"{marker}{j+1:3d}: {lines[j]}")
                break
        
        return True
        
    except Exception as e:
        print(f"❌ 代码修改验证失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始验证 /sessions/send 接口修改")
    
    success1 = test_handle_exception_function()
    success2 = test_code_modification()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败")
    
    print("\n📋 修改总结:")
    print("1. ✅ 在 /sessions/send 接口中添加了文件内容长度检查")
    print("2. ✅ 检测到文件内容超长时，直接返回 handle_exception(resource_error, request_id)")
    print("3. ✅ 不再抛出异常到外层，避免了异常传播")
    print("4. ✅ 保持了统一的错误响应格式")
    
    print("\n🎯 API层异常处理机制总结:")
    print("- 📍 位置: src/presentation/api/dependencies/api_common_utils.py")
    print("- 🔧 实现: handle_exception(e: Exception, request_id: str) -> dict")
    print("- 🎨 方式: 工具函数式异常处理，在每个路由中手动调用")
    print("- 📦 格式: 统一的 package_api_result 格式")
    print("- 🆔 支持: FileContentTooLargeError、ClientException、通用异常")


if __name__ == "__main__":
    main()
