#!/usr/bin/env python3
"""
测试 AuthService 中新增的 session_token 支持
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_extract_auth_params_signature():
    """测试_extract_auth_params方法签名"""
    print("🧪 测试 _extract_auth_params 方法签名")
    print("=" * 50)
    
    try:
        # 读取auth_service.py文件
        auth_service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'auth_service.py')
        
        with open(auth_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查方法签名修改
        signature_checks = [
            ('async def _extract_auth_params(self, request: Request) -> tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:', "方法签名包含4个返回值"),
            ('Returns:', "包含Returns文档"),
            ('tuple: (login_token, login_session_id, region_id, session_token)', "返回值文档包含session_token")
        ]
        
        print("📝 方法签名检查:")
        all_passed = True
        
        for check, desc in signature_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False


def test_session_token_extraction():
    """测试session_token提取逻辑"""
    print("\n🧪 测试 session_token 提取逻辑")
    print("=" * 50)
    
    try:
        # 读取auth_service.py文件
        auth_service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'auth_service.py')
        
        with open(auth_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查session_token提取逻辑
        extraction_checks = [
            # 查询参数提取
            ('session_token = (request.query_params.get("SessionToken") or', "查询参数SessionToken提取"),
            ('request.query_params.get("sessionToken"))', "查询参数sessionToken提取"),
            
            # 请求头提取
            ('session_token = (request.headers.get("X-Session-Token") or', "请求头X-Session-Token提取"),
            ('request.headers.get("X-session-token"))', "请求头X-session-token提取"),
            
            # JSON body提取
            ('session_token = (json_data.get("SessionToken") or', "JSON body SessionToken提取"),
            ('json_data.get("sessionToken"))', "JSON body sessionToken提取"),
            
            # 返回值
            ('return login_token, login_session_id, region_id, session_token', "返回session_token")
        ]
        
        print("📝 session_token提取逻辑检查:")
        all_passed = True
        
        for check, desc in extraction_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ session_token提取逻辑测试失败: {e}")
        return False


def test_get_current_user_logic():
    """测试get_current_user方法逻辑"""
    print("\n🧪 测试 get_current_user 方法逻辑")
    print("=" * 50)
    
    try:
        # 读取auth_service.py文件
        auth_service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'auth_service.py')
        
        with open(auth_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查get_current_user方法的修改
        logic_checks = [
            # 参数提取
            ('login_token, login_session_id, region_id, session_token = await self._extract_auth_params(request)', "提取4个参数"),
            ('session_token={bool(session_token)}', "日志包含session_token"),
            
            # session_token优先逻辑
            ('if session_token and self.login_verify_client is not None:', "session_token优先判断"),
            ('logger.info(f"[AuthService] 使用session_token进行认证验证")', "session_token认证日志"),
            ('login_session_id=session_token', "session_token传递给login_session_id"),
            
            # 原有逻辑保持
            ('elif (login_token or login_session_id) and self.login_verify_client is not None:', "原有逻辑用elif"),
            ('logger.info(f"[AuthService] 使用login_token/login_session_id进行认证验证")', "原有认证日志"),
            
            # 错误处理
            ('logger.error(f"[AuthService] session_token认证验证失败: {e}")', "session_token错误处理")
        ]
        
        print("📝 get_current_user逻辑检查:")
        all_passed = True
        
        for check, desc in logic_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ get_current_user逻辑测试失败: {e}")
        return False


def test_session_token_priority():
    """测试session_token优先级逻辑"""
    print("\n🧪 测试 session_token 优先级逻辑")
    print("=" * 50)
    
    try:
        # 读取auth_service.py文件
        auth_service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'auth_service.py')
        
        with open(auth_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找get_current_user方法
        method_start = content.find('async def get_current_user(')
        if method_start == -1:
            print("❌ 未找到get_current_user方法")
            return False
        
        method_end = content.find('async def ', method_start + 1)
        if method_end == -1:
            method_end = len(content)
        
        method_content = content[method_start:method_end]
        
        # 检查优先级逻辑
        priority_checks = [
            # session_token优先
            ('if session_token and self.login_verify_client is not None:', "session_token优先检查"),
            ('login_session_id=session_token', "只传递session_token给login_session_id"),
            
            # 确保session_token分支不传递其他参数
            ('elif (login_token or login_session_id) and self.login_verify_client is not None:', "其他参数用elif分支"),
        ]
        
        print("📝 优先级逻辑检查:")
        all_passed = True
        
        for check, desc in priority_checks:
            if check in method_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查session_token分支是否只传递了login_session_id
        session_token_branch_start = method_content.find('if session_token and self.login_verify_client is not None:')
        elif_branch_start = method_content.find('elif (login_token or login_session_id) and self.login_verify_client is not None:')
        
        if session_token_branch_start != -1 and elif_branch_start != -1:
            session_token_branch = method_content[session_token_branch_start:elif_branch_start]
            
            # 检查session_token分支是否只传递了login_session_id参数
            if 'login_session_id=session_token' in session_token_branch:
                if 'login_token=' not in session_token_branch and 'region_id=' not in session_token_branch:
                    print(f"   ✅ session_token分支只传递login_session_id参数")
                else:
                    print(f"   ❌ session_token分支传递了多余的参数")
                    all_passed = False
            else:
                print(f"   ❌ session_token分支未正确传递参数")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 优先级逻辑测试失败: {e}")
        return False


def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试向后兼容性")
    print("=" * 50)
    
    try:
        # 读取auth_service.py文件
        auth_service_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'auth_service.py')
        
        with open(auth_service_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查向后兼容性
        compatibility_checks = [
            # 原有参数仍然支持
            ('login_token = (request.query_params.get("LoginToken") or', "仍支持LoginToken查询参数"),
            ('login_session_id = (request.query_params.get("LoginSessionId") or', "仍支持LoginSessionId查询参数"),
            ('region_id = (request.query_params.get("RegionId") or', "仍支持RegionId查询参数"),
            
            # 原有请求头仍然支持
            ('login_token = (request.headers.get("X-Login-Token") or', "仍支持X-Login-Token请求头"),
            ('login_session_id = (request.headers.get("X-Login-Session-Id") or', "仍支持X-Login-Session-Id请求头"),
            
            # 原有JSON body仍然支持
            ('login_token = (json_data.get("LoginToken") or', "仍支持LoginToken JSON参数"),
            ('login_session_id = (json_data.get("LoginSessionId") or', "仍支持LoginSessionId JSON参数"),
            
            # 原有认证逻辑保持
            ('elif (login_token or login_session_id) and self.login_verify_client is not None:', "原有认证逻辑保持"),
            ('login_token=login_token,', "原有参数传递保持"),
            ('region_id=region_id', "region_id参数传递保持")
        ]
        
        print("📝 向后兼容性检查:")
        all_passed = True
        
        for check, desc in compatibility_checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 AuthService session_token 支持")
    
    tests = [
        test_extract_auth_params_signature,
        test_session_token_extraction,
        test_get_current_user_logic,
        test_session_token_priority,
        test_backward_compatibility
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！session_token 支持添加成功")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 功能实现总结:")
    print("1. ✅ 修改了_extract_auth_params方法，支持提取session_token")
    print("2. ✅ 支持从查询参数、请求头、JSON body提取session_token")
    print("3. ✅ 修改了get_current_user方法，优先使用session_token")
    print("4. ✅ session_token存在时，忽略其他认证参数")
    print("5. ✅ 保持了向后兼容性，原有逻辑不受影响")
    
    print("\n🎯 使用方式:")
    print("- 查询参数: ?SessionToken=xxx 或 ?sessionToken=xxx")
    print("- 请求头: X-Session-Token: xxx 或 X-session-token: xxx")
    print("- JSON body: {\"SessionToken\": \"xxx\"} 或 {\"sessionToken\": \"xxx\"}")
    
    print("\n🔄 认证优先级:")
    print("1. session_token (最高优先级)")
    print("2. login_token + login_session_id + region_id (原有逻辑)")
    
    print("\n💡 调用逻辑:")
    print("- 有session_token: verify_login_token(login_session_id=session_token)")
    print("- 无session_token: verify_login_token(login_session_id=xxx, login_token=xxx, region_id=xxx)")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
