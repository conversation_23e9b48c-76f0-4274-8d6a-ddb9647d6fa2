#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ValueError异常处理修复
验证handle_exception函数能正确处理ValueError并返回有意义的错误信息
"""

import sys
import os
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_valueerror_handling():
    """测试ValueError异常处理"""
    logger.info("🧪 测试ValueError异常处理")
    
    try:
        from src.presentation.api.dependencies.api_common_utils import handle_exception
        
        # 测试1: 会话不存在或无权限访问的ValueError
        session_id = "test_session_123"
        error1 = ValueError(f"会话不存在或无权限访问: {session_id}")
        result1 = handle_exception(error1, "test_request_001")
        
        logger.info("✅ 测试1 - 会话权限错误:")
        logger.info(f"   错误码: {result1.get('code')}")
        logger.info(f"   错误消息: {result1.get('message')}")
        logger.info(f"   成功标志: {result1.get('success')}")
        logger.info(f"   HTTP状态: {result1.get('status')}")
        
        # 验证返回结果
        assert result1.get('code') == 'INVALID_REQUEST', f"期望错误码为INVALID_REQUEST，实际为{result1.get('code')}"
        assert result1.get('message') == f"会话不存在或无权限访问: {session_id}", f"错误消息不匹配"
        assert result1.get('success') is False, "成功标志应该为False"
        assert result1.get('status') == 400, f"HTTP状态码应该为400，实际为{result1.get('status')}"
        
        # 测试2: 其他类型的ValueError
        error2 = ValueError("参数格式不正确")
        result2 = handle_exception(error2, "test_request_002")
        
        logger.info("✅ 测试2 - 参数格式错误:")
        logger.info(f"   错误码: {result2.get('code')}")
        logger.info(f"   错误消息: {result2.get('message')}")
        logger.info(f"   成功标志: {result2.get('success')}")
        
        # 验证返回结果
        assert result2.get('code') == 'INVALID_REQUEST', f"期望错误码为INVALID_REQUEST，实际为{result2.get('code')}"
        assert result2.get('message') == "参数格式不正确", f"错误消息不匹配"
        assert result2.get('success') is False, "成功标志应该为False"
        
        # 测试3: 空的ValueError
        error3 = ValueError("")
        result3 = handle_exception(error3, "test_request_003")
        
        logger.info("✅ 测试3 - 空错误消息:")
        logger.info(f"   错误码: {result3.get('code')}")
        logger.info(f"   错误消息: '{result3.get('message')}'")
        logger.info(f"   成功标志: {result3.get('success')}")
        
        # 验证返回结果
        assert result3.get('code') == 'INVALID_REQUEST', f"期望错误码为INVALID_REQUEST，实际为{result3.get('code')}"
        assert result3.get('message') == "", f"错误消息应该为空字符串"
        assert result3.get('success') is False, "成功标志应该为False"
        
        # 测试4: 对比其他异常类型（确保不受影响）
        error4 = Exception("通用异常测试")
        result4 = handle_exception(error4, "test_request_004")
        
        logger.info("✅ 测试4 - 通用异常（对比测试）:")
        logger.info(f"   错误码: {result4.get('code')}")
        logger.info(f"   错误消息: {result4.get('message')}")
        logger.info(f"   成功标志: {result4.get('success')}")
        
        # 验证通用异常仍然返回通用错误消息
        assert result4.get('code') == 500, f"期望错误码为500，实际为{result4.get('code')}"
        assert result4.get('message') == "Sorry, something went wrong, please contact the administrator.", f"通用异常消息不匹配"
        assert result4.get('success') is False, "成功标志应该为False"
        
        logger.info("🎉 所有ValueError处理测试通过！")
        return True
        
    except Exception as e:
        logger.error(f"❌ ValueError处理测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def test_session_routes_integration():
    """测试session_routes中的集成效果"""
    logger.info("\n🧪 测试session_routes集成效果")
    
    try:
        # 模拟session_routes中的错误处理逻辑
        from src.presentation.api.dependencies.api_common_utils import handle_exception
        
        # 模拟会话验证失败的场景
        session_id = "sess_invalid_123"
        
        try:
            # 模拟会话验证逻辑
            raise ValueError(f"会话不存在或无权限访问: {session_id}")
        except Exception as e:
            # 使用handle_exception处理
            result = handle_exception(e, "mock_request_id")
            
            logger.info("✅ session_routes集成测试:")
            logger.info(f"   原始异常: {e}")
            logger.info(f"   处理后错误码: {result.get('code')}")
            logger.info(f"   处理后错误消息: {result.get('message')}")
            logger.info(f"   HTTP状态码: {result.get('status')}")
            
            # 验证前端能收到正确的错误信息
            expected_message = f"会话不存在或无权限访问: {session_id}"
            assert result.get('message') == expected_message, f"错误消息不匹配，期望: {expected_message}, 实际: {result.get('message')}"
            
            logger.info("✅ 前端现在能收到正确的错误提示了！")
            return True
            
    except Exception as e:
        logger.error(f"❌ session_routes集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始测试ValueError异常处理修复")
    
    success1 = test_valueerror_handling()
    success2 = test_session_routes_integration()
    
    logger.info("\n" + "=" * 60)
    if success1 and success2:
        logger.info("🎉 所有测试通过！")
        
        logger.info("\n📋 修复总结:")
        logger.info("1. ✅ 在handle_exception函数中添加了ValueError的专门处理")
        logger.info("2. ✅ ValueError异常现在返回INVALID_REQUEST错误码")
        logger.info("3. ✅ ValueError异常的错误消息会直接传递给前端")
        logger.info("4. ✅ HTTP状态码设置为400（客户端错误）")
        logger.info("5. ✅ 其他异常类型的处理逻辑保持不变")
        
        logger.info("\n🎯 问题解决:")
        logger.info("- 之前: ValueError异常返回通用错误 'Sorry, something went wrong...'")
        logger.info("- 现在: ValueError异常返回具体错误信息，如 '会话不存在或无权限访问: session_id'")
        logger.info("- 前端用户现在能看到有意义的错误提示了！")
        
        logger.info("\n📝 修改文件:")
        logger.info("- src/presentation/api/dependencies/api_common_utils.py")
        logger.info("  添加了elif isinstance(e, ValueError)的处理分支")
        
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
