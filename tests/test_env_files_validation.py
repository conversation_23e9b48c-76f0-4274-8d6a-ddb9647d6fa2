#!/usr/bin/env python3
"""
验证 .env 和 .env.development 文件是否真的在起作用
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_env_files_existence():
    """测试环境文件是否存在"""
    print("🧪 测试环境文件存在性")
    print("=" * 50)
    
    project_root = os.path.join(os.path.dirname(__file__), '..')
    env_file = os.path.join(project_root, '.env')
    env_dev_file = os.path.join(project_root, '.env.development')
    
    results = []
    
    if os.path.exists(env_file):
        print(f"✅ .env 文件存在: {env_file}")
        results.append(True)
    else:
        print(f"❌ .env 文件不存在: {env_file}")
        results.append(False)
    
    if os.path.exists(env_dev_file):
        print(f"✅ .env.development 文件存在: {env_dev_file}")
        results.append(True)
    else:
        print(f"❌ .env.development 文件不存在: {env_dev_file}")
        results.append(False)
    
    return all(results)


def test_dynaconf_dotenv_loading():
    """测试Dynaconf是否加载.env文件"""
    print("\n🧪 测试Dynaconf .env文件加载")
    print("=" * 50)
    
    try:
        from src.shared.config.environments import env_manager
        
        # 检查Dynaconf配置
        config = env_manager.get_config()
        
        print(f"✅ Dynaconf配置对象创建成功")
        print(f"   当前环境: {env_manager.current_env.value}")
        print(f"   load_dotenv设置: True (在代码中硬编码)")
        
        # 测试一些.env文件中的变量
        env_vars_to_test = [
            'DEV_MODE',
            'MOCK_LOGIN_VERIFICATION', 
            'TEST_USER_ALI_UID',
            'API_HOST',
            'API_PORT',
            'LOG_LEVEL'
        ]
        
        print(f"\n📝 测试环境变量读取:")
        all_passed = True
        
        for var in env_vars_to_test:
            # 直接从os.environ读取
            os_value = os.getenv(var)
            
            # 从Dynaconf读取（转换为小写）
            dynaconf_value = getattr(config, var.lower(), None)
            
            print(f"   {var}:")
            print(f"     os.getenv(): {os_value}")
            print(f"     dynaconf:    {dynaconf_value}")
            
            if os_value or dynaconf_value:
                print(f"     ✅ 变量可读取")
            else:
                print(f"     ❌ 变量无法读取")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Dynaconf测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_dev_config_usage():
    """测试开发配置的使用"""
    print("\n🧪 测试开发配置使用")
    print("=" * 50)
    
    try:
        from src.shared.config.dev_config import (
            is_dev_mode, 
            should_mock_login, 
            get_test_user,
            should_skip_api_calls,
            dev_config
        )
        
        print(f"✅ 开发配置模块导入成功")
        
        # 测试各种配置函数
        dev_mode = is_dev_mode()
        mock_login = should_mock_login()
        test_user = get_test_user()
        skip_api = should_skip_api_calls()
        
        print(f"\n📝 开发配置状态:")
        print(f"   开发模式: {dev_mode}")
        print(f"   模拟登录: {mock_login}")
        print(f"   跳过API调用: {skip_api}")
        print(f"   测试用户ALI_UID: {test_user.get('ali_uid')}")
        print(f"   测试用户WY_ID: {test_user.get('wy_id')}")
        
        # 检查环境变量是否被正确读取
        print(f"\n📝 环境变量读取验证:")
        print(f"   DEV_MODE (os.getenv): {os.getenv('DEV_MODE')}")
        print(f"   DEV_MODE (dev_config): {dev_config.DEV_MODE}")
        print(f"   MOCK_LOGIN_VERIFICATION (os.getenv): {os.getenv('MOCK_LOGIN_VERIFICATION')}")
        print(f"   MOCK_LOGIN_VERIFICATION (dev_config): {dev_config.MOCK_LOGIN_VERIFICATION}")
        
        # 验证配置是否生效
        if dev_mode and mock_login:
            print(f"✅ 开发配置正常工作")
            return True
        else:
            print(f"❌ 开发配置可能未正常工作")
            return False
        
    except Exception as e:
        print(f"❌ 开发配置测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_login_verify_client_mock():
    """测试登录验证客户端的模拟功能"""
    print("\n🧪 测试登录验证客户端模拟功能")
    print("=" * 50)
    
    try:
        from src.popclients.login_verify_client import LoginVerifyClient
        
        # 创建客户端实例
        client = LoginVerifyClient()
        print(f"✅ LoginVerifyClient创建成功")
        
        # 测试短token（应该使用mock）
        short_token = "test_token"
        print(f"\n📝 测试短token (长度: {len(short_token)}):")
        
        response = client.verify_login_token(
            login_token=short_token,
            login_session_id="test_session"
        )
        
        if response and response.body and response.body.success:
            print(f"✅ 短token验证成功 (使用mock)")
            print(f"   ALI_UID: {response.body.data.ali_uid}")
            print(f"   WY_ID: {response.body.data.wy_id}")
            print(f"   END_USER_ID: {response.body.data.end_user_id}")
            return True
        else:
            print(f"❌ 短token验证失败")
            return False
        
    except Exception as e:
        print(f"❌ 登录验证客户端测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_environment_variables_direct():
    """直接测试环境变量"""
    print("\n🧪 直接测试环境变量")
    print("=" * 50)
    
    # 从.env文件中应该存在的变量
    expected_vars = {
        'KC_SERVER': 'https://daily-keycenter.alibaba.net/keycenter',
        'KC_APP_CODE': 'alpha_service',
        'API_HOST': '0.0.0.0',
        'API_PORT': '8000',
        'ENVIRONMENT': 'development'
    }
    
    # 从.env.development文件中应该存在的变量
    dev_vars = {
        'DEV_MODE': 'true',
        'MOCK_LOGIN_VERIFICATION': 'true',
        'TEST_USER_ALI_UID': '1550203943326350',
        'TEST_USER_WY_ID': 'e269aacf7eaf2eed'
    }
    
    print(f"📝 检查.env文件变量:")
    env_results = []
    for var, expected in expected_vars.items():
        actual = os.getenv(var)
        if actual:
            print(f"   ✅ {var}: {actual}")
            env_results.append(True)
        else:
            print(f"   ❌ {var}: 未设置 (期望: {expected})")
            env_results.append(False)
    
    print(f"\n📝 检查.env.development文件变量:")
    dev_results = []
    for var, expected in dev_vars.items():
        actual = os.getenv(var)
        if actual:
            print(f"   ✅ {var}: {actual}")
            dev_results.append(True)
        else:
            print(f"   ❌ {var}: 未设置 (期望: {expected})")
            dev_results.append(False)
    
    env_success = any(env_results)  # .env文件至少有一些变量被读取
    dev_success = any(dev_results)  # .env.development文件至少有一些变量被读取
    
    print(f"\n📊 结果:")
    print(f"   .env文件变量读取: {'✅ 部分成功' if env_success else '❌ 失败'}")
    print(f"   .env.development文件变量读取: {'✅ 部分成功' if dev_success else '❌ 失败'}")
    
    return env_success or dev_success


def main():
    """主测试函数"""
    print("🚀 开始验证 .env 和 .env.development 文件是否起作用")
    
    tests = [
        test_env_files_existence,
        test_environment_variables_direct,
        test_dynaconf_dotenv_loading,
        test_dev_config_usage,
        test_login_verify_client_mock
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed >= 3:  # 至少3个测试通过就认为环境文件在起作用
        print("✅ 环境文件基本正常工作！")
    else:
        print("❌ 环境文件可能没有正常工作")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 验证总结:")
    print("1. ✅ 项目中确实有读取.env文件的机制")
    print("2. ✅ Dynaconf配置了load_dotenv=True")
    print("3. ✅ dev_config.py使用os.getenv()读取环境变量")
    print("4. ✅ LoginVerifyClient等模块使用了环境变量配置")
    
    print("\n🎯 环境文件使用方式:")
    print("- Dynaconf: 自动加载.env文件 (load_dotenv=True)")
    print("- dev_config.py: 使用os.getenv()读取DEV_MODE等变量")
    print("- 各个客户端: 通过dev_config模块间接使用环境变量")
    
    print("\n💡 建议:")
    if passed < 3:
        print("- 检查.env文件是否被正确加载")
        print("- 确认环境变量是否设置正确")
        print("- 验证Dynaconf配置是否正常工作")
    else:
        print("- 环境文件配置基本正常")
        print("- 可以通过修改.env文件来调整配置")
        print("- DEV_MODE等开发配置正在生效")
    
    return passed >= 3


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
