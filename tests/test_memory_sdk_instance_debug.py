#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试MemorySDK实例问题
检查是否有多个实例或初始化问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def debug_memory_sdk_instances():
    """调试MemorySDK实例"""
    print("🔍 调试MemorySDK实例问题")
    print("=" * 50)
    
    try:
        # 导入全局实例
        from src.infrastructure.memory.memory_sdk import memory_sdk
        print(f"📋 全局MemorySDK实例:")
        print(f"   实例ID: {id(memory_sdk)}")
        print(f"   MessageProcessor: {memory_sdk.message_processor}")
        if memory_sdk.message_processor:
            print(f"   MessageProcessor类型: {type(memory_sdk.message_processor)}")
            print(f"   MessageProcessor ID: {id(memory_sdk.message_processor)}")
        
        # 导入SessionService
        from src.domain.services.session_service import session_service
        print(f"\n📋 SessionService:")
        print(f"   实例ID: {id(session_service)}")
        print(f"   MemorySDK实例ID: {id(session_service.memory_sdk)}")
        print(f"   MessageProcessor: {session_service.message_processor}")
        if session_service.message_processor:
            print(f"   MessageProcessor类型: {type(session_service.message_processor)}")
            print(f"   MessageProcessor ID: {id(session_service.message_processor)}")
        
        # 检查是否是同一个实例
        print(f"\n🔍 实例对比:")
        print(f"   全局MemorySDK == SessionService.memory_sdk: {memory_sdk is session_service.memory_sdk}")
        
        if memory_sdk.message_processor and session_service.message_processor:
            print(f"   全局MemorySDK.message_processor == SessionService.message_processor: {memory_sdk.message_processor is session_service.message_processor}")
        
        # 测试创建新的MemorySDK实例
        from src.infrastructure.memory.memory_sdk import MemorySDK
        new_instance = MemorySDK()
        print(f"\n📋 新创建的MemorySDK实例:")
        print(f"   实例ID: {id(new_instance)}")
        print(f"   与全局实例相同: {new_instance is memory_sdk}")
        print(f"   MessageProcessor: {new_instance.message_processor}")
        
        # 检查初始化状态
        print(f"\n📋 初始化状态:")
        print(f"   全局MemorySDK._initialized: {getattr(memory_sdk, '_initialized', 'N/A')}")
        print(f"   新实例._initialized: {getattr(new_instance, '_initialized', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_message_processor_setting():
    """测试MessageProcessor设置"""
    print(f"\n🔧 测试MessageProcessor设置")
    print("-" * 30)
    
    try:
        from src.infrastructure.memory.memory_sdk import memory_sdk
        from src.domain.services.session_service import session_service
        
        # 检查当前状态
        print(f"当前状态:")
        print(f"   MemorySDK.message_processor: {memory_sdk.message_processor}")
        print(f"   SessionService.message_processor: {session_service.message_processor}")
        
        # 如果MemorySDK的MessageProcessor为None，尝试重新设置
        if memory_sdk.message_processor is None and session_service.message_processor is not None:
            print(f"\n🔄 重新设置MessageProcessor...")
            memory_sdk.set_message_processor(session_service.message_processor)
            print(f"   设置后MemorySDK.message_processor: {memory_sdk.message_processor}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success1 = debug_memory_sdk_instances()
    success2 = test_message_processor_setting()
    
    if success1 and success2:
        print(f"\n🎉 调试完成！")
    else:
        print(f"\n💥 调试失败")


if __name__ == "__main__":
    main()
