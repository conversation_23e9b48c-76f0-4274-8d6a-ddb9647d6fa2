#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集成测试：验证完整的消息流程
测试 /send -> /stream 的完整流程，验证消息能否正确接收和处理
"""

import asyncio
import json
import time
import sys
import os
from typing import Optional
import httpx
import uuid

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class MessageFlowTester:
    """消息流程测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id: Optional[str] = None
        
    async def test_send_message(self, agent_id: str = "alpha", message: str = "测试消息1234124") -> str:
        """测试发送消息接口"""
        print(f"\n=== 测试发送消息 ===")
        print(f"Agent ID: {agent_id}")
        print(f"消息内容: {message}")
        
        # 构造请求数据
        request_data = {
            "agent_id": agent_id,
            "prompt": message,
            "session_id": None,  # 创建新会话
            "desktop_id": None,
            "auth_code": None,
            "resources": []
        }
        
        # 构造请求头（模拟认证）
        headers = {
            "Content-Type": "application/json",
            "X-Request-ID": str(uuid.uuid4()),
            # 模拟认证头
            "Authorization": "Bearer test_token",
            "X-Ali-Uid": "test_user_123",
            "X-Wy-Id": "test_wy_456"
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/sessions/send",
                    json=request_data,
                    headers=headers
                )
                
                print(f"发送消息响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"发送消息响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    # 提取session_id
                    if result.get("success") and result.get("data", {}).get("session_id"):
                        self.session_id = result["data"]["session_id"]
                        print(f"✅ 获得Session ID: {self.session_id}")
                        return self.session_id
                    else:
                        print(f"❌ 发送消息失败: {result}")
                        return None
                else:
                    print(f"❌ 发送消息失败: HTTP {response.status_code}")
                    print(f"响应内容: {response.text}")
                    return None
                    
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
            return None
    
    async def test_stream_connection(self, session_id: str, duration: int = 30) -> bool:
        """测试SSE流连接"""
        print(f"\n=== 测试SSE流连接 ===")
        print(f"Session ID: {session_id}")
        print(f"监听时长: {duration}秒")
        
        # 构造请求头
        headers = {
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "X-Request-ID": str(uuid.uuid4()),
            # 模拟认证头
            "Authorization": "Bearer test_token",
            "X-Ali-Uid": "test_user_123",
            "X-Wy-Id": "test_wy_456"
        }
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "GET",
                    f"{self.base_url}/api/sessions/stream",
                    params={"session_id": session_id},
                    headers=headers
                ) as response:
                    
                    print(f"SSE连接状态: {response.status_code}")
                    
                    if response.status_code != 200:
                        print(f"❌ SSE连接失败: HTTP {response.status_code}")
                        print(f"响应内容: {await response.aread()}")
                        return False
                    
                    print("✅ SSE连接建立成功，开始监听消息...")
                    
                    message_count = 0
                    start_time = time.time()
                    
                    async for chunk in response.aiter_text():
                        if chunk.strip():
                            current_time = time.time()
                            elapsed = current_time - start_time
                            
                            print(f"[{elapsed:.1f}s] 收到数据: {chunk.strip()}")
                            
                            # 尝试解析消息
                            try:
                                if chunk.startswith("data: "):
                                    data_str = chunk[6:].strip()
                                    if data_str and data_str != "":
                                        data = json.loads(data_str)
                                        message_type = data.get("type", "unknown")
                                        
                                        if message_type == "heartbeat":
                                            print(f"  💓 心跳消息")
                                        elif message_type in ["TEXT_MESSAGE_CONTENT", "TEXT_MESSAGE_DELTA_CONTENT"]:
                                            message_count += 1
                                            content = data.get("content", "")
                                            print(f"  📝 消息内容 #{message_count}: {content[:100]}...")
                                        elif message_type in ["RUN_FINISHED", "RUN_ERROR"]:
                                            print(f"  🏁 会话完成: {message_type}")
                                            print("✅ 检测到会话完成，连接即将关闭")
                                            return True
                                        else:
                                            print(f"  📨 其他消息: {message_type}")
                                            
                            except json.JSONDecodeError:
                                print(f"  ⚠️ 无法解析的数据: {chunk.strip()}")
                            
                            # 检查是否超时
                            if elapsed > duration:
                                print(f"⏰ 监听时间达到 {duration}秒，结束测试")
                                break
                    
                    print(f"📊 测试结果: 共收到 {message_count} 条消息")
                    return message_count > 0
                    
        except Exception as e:
            print(f"❌ SSE连接异常: {e}")
            return False
    
    async def run_full_test(self, agent_id: str = "alpha", message: str = "测试消息1234124"):
        """运行完整的消息流程测试"""
        print("🚀 开始完整的消息流程测试")
        print("=" * 50)
        
        # 步骤1: 发送消息
        session_id = await self.test_send_message(agent_id, message)
        if not session_id:
            print("❌ 发送消息失败，测试终止")
            return False
        
        # 等待一下，让消息处理有时间启动
        print("\n⏳ 等待3秒让消息处理启动...")
        await asyncio.sleep(3)
        
        # 步骤2: 建立SSE连接
        success = await self.test_stream_connection(session_id, duration=60)
        
        if success:
            print("\n🎉 完整的消息流程测试成功！")
            print("✅ 消息发送成功")
            print("✅ SSE连接建立成功")
            print("✅ 消息接收成功")
        else:
            print("\n❌ 消息流程测试失败")
            print("可能的原因:")
            print("1. 消息处理逻辑有问题")
            print("2. SSE连接建立失败")
            print("3. 消息没有正确推送")
        
        return success


async def test_server_health():
    """测试服务器健康状态"""
    print("🔍 检查服务器健康状态...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://localhost:8000/status.taobao")
            
            if response.status_code == 200 and response.text.strip() == "success":
                print("✅ 服务器健康状态正常")
                return True
            else:
                print(f"❌ 服务器健康检查失败: {response.status_code} - {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保服务器正在运行在 http://localhost:8000")
        return False


async def main():
    """主测试函数"""
    print("🧪 Alpha Service 消息流程集成测试")
    print("=" * 60)
    
    # 检查服务器状态
    if not await test_server_health():
        print("\n❌ 服务器不可用，请先启动服务器")
        return
    
    # 创建测试器
    tester = MessageFlowTester()
    
    # 运行测试
    success = await tester.run_full_test(
        agent_id="alpha",
        message="你好，这是一个测试消息，请回复确认收到。当前时间：" + str(time.time())
    )
    
    if success:
        print("\n🎊 所有测试通过！消息流程工作正常")
    else:
        print("\n💥 测试失败，请检查日志和配置")


if __name__ == "__main__":
    asyncio.run(main())
