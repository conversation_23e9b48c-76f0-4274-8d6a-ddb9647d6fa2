#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控MemorySDK运行时状态
用于调试MessageProcessor在运行时丢失的问题
"""

import asyncio
import time
import sys
import os
import threading
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class MemorySDKMonitor:
    """MemorySDK监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
    async def start_monitoring(self, interval: int = 10):
        """开始监控"""
        print(f"🔍 开始监控MemorySDK状态，检查间隔: {interval}秒")
        self.monitoring = True
        
        try:
            from src.infrastructure.memory.memory_sdk import memory_sdk
            from src.domain.services.session_service import session_service
            
            while self.monitoring:
                await self._check_status(memory_sdk, session_service)
                await asyncio.sleep(interval)
                
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            import traceback
            traceback.print_exc()
    
    async def _check_status(self, memory_sdk, session_service):
        """检查状态"""
        current_time = time.time()
        print(f"\n📊 状态检查 - {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}")
        print("-" * 60)
        
        # 检查MemorySDK状态
        print(f"🔧 MemorySDK状态:")
        print(f"   实例ID: {id(memory_sdk)}")
        print(f"   MessageProcessor: {memory_sdk.message_processor}")
        print(f"   MessageProcessor ID: {id(memory_sdk.message_processor) if memory_sdk.message_processor else 'None'}")
        
        if hasattr(memory_sdk, '_message_processor_set_time'):
            set_time = memory_sdk._message_processor_set_time
            if set_time:
                elapsed = current_time - set_time
                print(f"   设置时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(set_time))}")
                print(f"   距离设置: {elapsed:.1f}秒")
            else:
                print(f"   设置时间: 未设置")
        
        if hasattr(memory_sdk, '_message_count'):
            print(f"   消息计数: {memory_sdk._message_count}")
        
        print(f"   进程ID: {os.getpid()}")
        print(f"   线程ID: {threading.current_thread().ident}")
        
        # 检查SessionService状态
        print(f"\n🔧 SessionService状态:")
        print(f"   实例ID: {id(session_service)}")
        print(f"   MessageProcessor: {session_service.message_processor}")
        print(f"   MessageProcessor ID: {id(session_service.message_processor) if session_service.message_processor else 'None'}")
        
        # 检查一致性
        print(f"\n🔍 一致性检查:")
        memory_sdk_same = memory_sdk is session_service.memory_sdk
        processor_same = (memory_sdk.message_processor is session_service.message_processor 
                         if memory_sdk.message_processor and session_service.message_processor 
                         else False)
        
        print(f"   MemorySDK实例一致: {'✅' if memory_sdk_same else '❌'}")
        print(f"   MessageProcessor一致: {'✅' if processor_same else '❌'}")
        
        # 检查潜在问题
        if memory_sdk.message_processor is None:
            print(f"\n⚠️ 发现问题: MemorySDK.message_processor 为 None")
            if session_service.message_processor is not None:
                print(f"   SessionService.message_processor 存在，可以恢复")
            else:
                print(f"   SessionService.message_processor 也为 None，严重问题！")
        
        # 内存引用检查
        import gc
        memory_sdk_refs = len(gc.get_referrers(memory_sdk))
        print(f"\n🧠 内存引用:")
        print(f"   MemorySDK引用数: {memory_sdk_refs}")
        
        if memory_sdk.message_processor:
            processor_refs = len(gc.get_referrers(memory_sdk.message_processor))
            print(f"   MessageProcessor引用数: {processor_refs}")
    
    def stop_monitoring(self):
        """停止监控"""
        print(f"\n🛑 停止监控")
        self.monitoring = False


async def run_monitor():
    """运行监控"""
    monitor = MemorySDKMonitor()
    
    try:
        # 启动监控
        await monitor.start_monitoring(interval=30)  # 每30秒检查一次
        
    except KeyboardInterrupt:
        print(f"\n⌨️ 用户中断")
    except Exception as e:
        print(f"\n❌ 监控失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        monitor.stop_monitoring()


def test_message_processor_persistence():
    """测试MessageProcessor持久性"""
    print(f"🧪 测试MessageProcessor持久性")
    print("=" * 50)
    
    try:
        from src.infrastructure.memory.memory_sdk import memory_sdk
        from src.domain.services.session_service import session_service
        
        # 记录初始状态
        initial_processor = memory_sdk.message_processor
        initial_processor_id = id(initial_processor) if initial_processor else None
        
        print(f"📋 初始状态:")
        print(f"   MessageProcessor: {initial_processor}")
        print(f"   MessageProcessor ID: {initial_processor_id}")
        
        # 模拟一些操作
        print(f"\n🔄 模拟操作...")
        
        # 强制垃圾回收
        import gc
        gc.collect()
        print(f"   执行垃圾回收")
        
        # 检查状态
        current_processor = memory_sdk.message_processor
        current_processor_id = id(current_processor) if current_processor else None
        
        print(f"\n📋 操作后状态:")
        print(f"   MessageProcessor: {current_processor}")
        print(f"   MessageProcessor ID: {current_processor_id}")
        
        # 比较
        if initial_processor_id == current_processor_id:
            print(f"✅ MessageProcessor保持一致")
        else:
            print(f"❌ MessageProcessor发生变化！")
            print(f"   初始ID: {initial_processor_id}")
            print(f"   当前ID: {current_processor_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print(f"🔍 MemorySDK运行时监控工具")
    print("=" * 60)
    
    # 先做持久性测试
    test_message_processor_persistence()
    
    print(f"\n" + "=" * 60)
    print(f"开始实时监控...")
    print(f"按 Ctrl+C 停止监控")
    
    # 运行监控
    asyncio.run(run_monitor())
