#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话文档file_id列表API接口
"""

import requests
import json


def test_session_document_relations_api():
    """测试获取会话文档file_id列表接口"""
    
    # 配置
    base_url = "http://localhost:8000"
    headers = {
        "Content-Type": "application/json",
        # 注意：这里需要根据实际的认证方式设置正确的headers
        # "Authorization": "Bearer your_token_here"
    }
    
    # 测试参数
    kb_id = "test_kb_001"
    session_id = "test_session_001"
    
    # 构造请求
    url = f"{base_url}/api/knowledge_base/session/document_relations"
    params = {
        "kb_id": kb_id,
        "session_id": session_id
    }
    
    try:
        print("=" * 60)
        print("测试会话文档file_id列表API接口")
        print("=" * 60)
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        
        # 发送请求
        response = requests.get(url, params=params, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            # 解析响应
            response_data = response.json()
            
            if response_data.get("success"):
                file_ids = response_data.get("data", [])
                print(f"\n✅ 成功获取file_id列表，共 {len(file_ids)} 个文件:")
                for i, file_id in enumerate(file_ids, 1):
                    print(f"  {i}. {file_id}")
                    
                # 验证返回的是字符串列表
                if isinstance(file_ids, list):
                    if all(isinstance(fid, str) for fid in file_ids):
                        print("\n✅ 数据格式验证通过：返回的是字符串列表")
                    else:
                        print("\n❌ 数据格式错误：列表中包含非字符串元素")
                else:
                    print("\n❌ 数据格式错误：返回的不是列表")
            else:
                print(f"\n❌ 请求失败: {response_data.get('message', '未知错误')}")
        else:
            print(f"\n❌ HTTP请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 网络请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"\n❌ JSON解析失败: {e}")
    except Exception as e:
        print(f"\n❌ 其他异常: {e}")
    
    print("\n" + "=" * 60)


def test_invalid_params():
    """测试无效参数"""
    base_url = "http://localhost:8000"
    url = f"{base_url}/api/knowledge_base/session/document_relations"
    
    test_cases = [
        {"kb_id": "", "session_id": "test_session", "desc": "空的kb_id"},
        {"kb_id": "test_kb", "session_id": "", "desc": "空的session_id"},
        {"session_id": "test_session", "desc": "缺少kb_id"},
        {"kb_id": "test_kb", "desc": "缺少session_id"},
    ]
    
    print("测试无效参数情况:")
    print("-" * 40)
    
    for case in test_cases:
        params = {k: v for k, v in case.items() if k != "desc"}
        try:
            response = requests.get(url, params=params)
            print(f"{case['desc']}: 状态码 {response.status_code}")
        except Exception as e:
            print(f"{case['desc']}: 异常 {e}")


if __name__ == "__main__":
    # 测试正常情况
    test_session_document_relations_api()
    
    # 测试异常情况
    test_invalid_params()
