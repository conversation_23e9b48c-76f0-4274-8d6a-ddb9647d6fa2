#!/usr/bin/env python3
"""
简单测试会话SSE连接断开API接口的核心逻辑
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_api_models():
    """测试API模型定义"""
    print("🧪 测试API模型定义")
    print("=" * 50)
    
    try:
        # 导入新的API模型
        from src.application.api_models import (
            SessionDisconnectRequest, 
            SessionDisconnectData, 
            SessionDisconnectResponse
        )
        
        # 测试请求模型
        request = SessionDisconnectRequest(session_id="test_session_123")
        print(f"✅ SessionDisconnectRequest 创建成功:")
        print(f"   session_id: {request.session_id}")
        
        # 测试响应数据模型
        data = SessionDisconnectData(
            session_id="test_session_123",
            message="SSE连接已断开"
        )
        print(f"✅ SessionDisconnectData 创建成功:")
        print(f"   session_id: {data.session_id}")
        print(f"   message: {data.message}")
        
        # 测试响应模型
        response = SessionDisconnectResponse(
            code=200,
            msg="操作成功",
            data=data
        )
        print(f"✅ SessionDisconnectResponse 创建成功:")
        print(f"   code: {response.code}")
        print(f"   msg: {response.msg}")
        print(f"   data: {response.data}")
        
        return True
        
    except Exception as e:
        print(f"❌ API模型测试失败: {e}")
        return False


def test_route_import():
    """测试路由导入"""
    print("\n🧪 测试路由导入")
    print("=" * 50)
    
    try:
        # 检查路由文件是否可以正常导入
        from src.presentation.api.routes import session_routes
        
        # 检查是否有disconnect_session函数
        if hasattr(session_routes, 'disconnect_session'):
            print("✅ disconnect_session 函数存在")
            
            # 检查函数签名
            import inspect
            sig = inspect.signature(session_routes.disconnect_session)
            params = list(sig.parameters.keys())
            
            expected_params = ['request', 'current_user', 'common_params', 'request_id']
            missing_params = [p for p in expected_params if p not in params]
            
            if not missing_params:
                print("✅ 函数参数签名正确")
                print(f"   参数: {params}")
            else:
                print(f"❌ 缺少参数: {missing_params}")
                return False
                
        else:
            print("❌ disconnect_session 函数不存在")
            return False
        
        # 检查路由器是否包含新的路由
        router = session_routes.router
        routes = [route.path for route in router.routes if hasattr(route, 'path')]
        
        if '/sessions/disconnect' in routes:
            print("✅ /sessions/disconnect 路由已注册")
        else:
            print("❌ /sessions/disconnect 路由未找到")
            print(f"   现有路由: {routes}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 路由导入测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_sse_manager_method():
    """测试SSE管理器方法"""
    print("\n🧪 测试SSE管理器方法")
    print("=" * 50)
    
    try:
        from src.domain.services.sse_manager import SSEManager
        
        # 创建SSE管理器实例
        sse_manager = SSEManager()
        
        # 检查close_session_connection方法是否存在
        if hasattr(sse_manager, 'close_session_connection'):
            print("✅ close_session_connection 方法存在")
            
            # 检查方法签名
            import inspect
            sig = inspect.signature(sse_manager.close_session_connection)
            params = list(sig.parameters.keys())
            
            if 'session_id' in params:
                print("✅ 方法参数正确")
                print(f"   参数: {params}")
            else:
                print(f"❌ 方法参数不正确: {params}")
                return False
                
        else:
            print("❌ close_session_connection 方法不存在")
            return False
        
        # 测试方法调用（不会实际执行，只是验证可以调用）
        try:
            # 这个调用应该不会抛出异常（即使没有实际连接）
            sse_manager.close_session_connection("test_session")
            print("✅ 方法调用成功（幂等操作）")
        except Exception as call_error:
            print(f"❌ 方法调用失败: {call_error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ SSE管理器测试失败: {e}")
        return False


def test_permission_integration():
    """测试权限集成"""
    print("\n🧪 测试权限集成")
    print("=" * 50)
    
    try:
        # 检查权限相关的导入
        from src.infrastructure.database.models.auth_models import PermissionType
        from src.domain.services.session_service import session_service
        
        # 检查session_service是否有权限检查方法
        if hasattr(session_service, 'get_session_with_permission_check_async'):
            print("✅ get_session_with_permission_check_async 方法存在")
            
            # 检查PermissionType.READ是否存在
            if hasattr(PermissionType, 'READ'):
                print("✅ PermissionType.READ 权限类型存在")
            else:
                print("❌ PermissionType.READ 权限类型不存在")
                return False
                
        else:
            print("❌ get_session_with_permission_check_async 方法不存在")
            return False
        
        # 检查session_service是否有sse_manager
        if hasattr(session_service, 'sse_manager'):
            print("✅ session_service.sse_manager 存在")
        else:
            print("❌ session_service.sse_manager 不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 权限集成测试失败: {e}")
        return False


def test_code_structure():
    """测试代码结构"""
    print("\n🧪 测试代码结构")
    print("=" * 50)
    
    try:
        # 读取路由文件内容
        route_file = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'routes', 'session_routes.py')
        
        with open(route_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键代码是否存在
        checks = [
            ('@router.post("/sessions/disconnect")', "包含断开连接路由装饰器"),
            ('async def disconnect_session(', "包含断开连接函数定义"),
            ('SessionDisconnectRequest', "包含请求模型导入"),
            ('get_session_with_permission_check_async', "包含权限检查调用"),
            ('sse_manager.close_session_connection', "包含SSE管理器调用"),
            ('PermissionType.READ', "包含READ权限检查"),
            ('package_api_result', "包含统一响应格式"),
            ('handle_exception', "包含异常处理")
        ]
        
        print("📝 代码结构检查:")
        all_passed = True
        for check, desc in checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 代码结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始简单测试会话SSE连接断开API接口")
    
    tests = [
        test_api_models,
        test_route_import,
        test_sse_manager_method,
        test_permission_integration,
        test_code_structure
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 接口实现总结:")
    print("1. ✅ 添加了 SessionDisconnectRequest/Data/Response 模型")
    print("2. ✅ 实现了 POST /api/sessions/disconnect 接口")
    print("3. ✅ 集成了用户权限验证（READ权限）")
    print("4. ✅ 调用了 sse_manager.close_session_connection() 方法")
    print("5. ✅ 实现了统一的错误处理和响应格式")
    print("6. ✅ 支持幂等操作（重复断开不报错）")
    
    print("\n🎯 接口特性:")
    print("- 路径: POST /api/sessions/disconnect")
    print("- 参数: session_id (会话ID)")
    print("- 权限: 需要对会话有READ权限")
    print("- 响应: 统一的API响应格式")
    print("- 日志: 完整的操作日志记录")
    print("- 安全: 只能断开自己有权限的会话连接")


if __name__ == "__main__":
    main()
