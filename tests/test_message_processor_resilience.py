#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MessageProcessor的弹性恢复机制
验证在MessageProcessor丢失时能否自动恢复
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_message_processor_resilience():
    """测试MessageProcessor弹性恢复"""
    print("🔧 测试MessageProcessor弹性恢复机制")
    print("=" * 50)
    
    try:
        from src.infrastructure.memory.memory_sdk import memory_sdk
        from src.domain.services.session_service import session_service
        
        print("📋 初始状态检查:")
        print(f"   MemorySDK实例ID: {id(memory_sdk)}")
        print(f"   MessageProcessor: {memory_sdk.message_processor}")
        print(f"   SessionService.MessageProcessor: {session_service.message_processor}")
        
        # 确保MessageProcessor已设置
        if memory_sdk.message_processor is None:
            print("⚠️ MessageProcessor未设置，先设置...")
            memory_sdk.set_message_processor(session_service.message_processor)
        
        initial_processor = memory_sdk.message_processor
        initial_processor_id = id(initial_processor) if initial_processor else None
        
        print(f"\n📋 设置后状态:")
        print(f"   MessageProcessor: {initial_processor}")
        print(f"   MessageProcessor ID: {initial_processor_id}")
        print(f"   备份存在: {hasattr(memory_sdk, '_message_processor_backup')}")
        
        # 模拟MessageProcessor丢失
        print(f"\n🔄 模拟MessageProcessor丢失...")
        memory_sdk.message_processor = None
        print(f"   MessageProcessor已设置为None")
        
        # 模拟消息到达，触发恢复机制
        print(f"\n📨 模拟消息处理，触发恢复机制...")
        
        # 创建模拟事件
        class MockEvent:
            def __init__(self):
                self.event_id = "test_resilience"
                self.type = "TEXT_MESSAGE_CONTENT"
                self.session_id = "test_session"
                self.run_id = "test_run"
                self.content = "测试弹性恢复"
            
            def __str__(self):
                return f"MockEvent(id={self.event_id})"
        
        mock_event = MockEvent()
        
        # 直接调用消息处理方法的前半部分（不实际处理消息）
        memory_sdk._message_count += 1
        print(f"收到模拟event: {mock_event}")
        
        # 执行强制健康检查
        recovery_result = memory_sdk._health_check(force=True)
        
        # 检查恢复结果
        recovered_processor = memory_sdk.message_processor
        recovered_processor_id = id(recovered_processor) if recovered_processor else None
        
        print(f"\n📋 恢复后状态:")
        print(f"   MessageProcessor: {recovered_processor}")
        print(f"   MessageProcessor ID: {recovered_processor_id}")
        print(f"   恢复结果: {recovery_result}")

        # 验证恢复效果
        if recovered_processor is not None and recovery_result:
            if recovered_processor_id == initial_processor_id:
                print(f"✅ MessageProcessor成功恢复，ID一致")
            else:
                print(f"✅ MessageProcessor成功恢复，但ID不同（可能从SessionService重新获取）")
            return True
        else:
            print(f"❌ MessageProcessor恢复失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backup_mechanism():
    """测试备份机制"""
    print(f"\n🔄 测试备份机制")
    print("-" * 30)
    
    try:
        from src.infrastructure.memory.memory_sdk import memory_sdk
        from src.domain.services.session_service import session_service
        
        # 确保有MessageProcessor
        if memory_sdk.message_processor is None:
            memory_sdk.set_message_processor(session_service.message_processor)
        
        # 检查备份是否存在
        has_backup = hasattr(memory_sdk, '_message_processor_backup')
        backup_valid = (has_backup and 
                       memory_sdk._message_processor_backup is not None and
                       memory_sdk._message_processor_backup is memory_sdk.message_processor)
        
        print(f"   备份存在: {has_backup}")
        print(f"   备份有效: {backup_valid}")
        
        if backup_valid:
            print(f"✅ 备份机制正常工作")
            return True
        else:
            print(f"❌ 备份机制有问题")
            return False
            
    except Exception as e:
        print(f"❌ 备份机制测试失败: {e}")
        return False


def test_health_check_timing():
    """测试健康检查时机"""
    print(f"\n⏰ 测试健康检查时机")
    print("-" * 30)
    
    try:
        from src.infrastructure.memory.memory_sdk import memory_sdk
        
        # 记录当前健康检查时间
        initial_check_time = getattr(memory_sdk, '_last_health_check', 0)
        print(f"   初始检查时间: {initial_check_time}")
        
        # 执行强制健康检查
        memory_sdk._health_check(force=True)
        
        # 检查时间是否更新
        updated_check_time = getattr(memory_sdk, '_last_health_check', 0)
        print(f"   更新检查时间: {updated_check_time}")
        
        if updated_check_time > initial_check_time:
            print(f"✅ 健康检查时间正确更新")
            return True
        else:
            print(f"❌ 健康检查时间未更新")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查时机测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 MessageProcessor弹性恢复机制测试")
    print("=" * 60)
    
    results = []
    
    # 运行测试
    results.append(test_message_processor_resilience())
    results.append(test_backup_mechanism())
    results.append(test_health_check_timing())
    
    # 总结结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 测试结果总结:")
    print(f"   通过: {passed}/{total}")
    
    if passed == total:
        print(f"🎉 所有测试通过！MessageProcessor弹性恢复机制工作正常")
        print(f"\n🔧 改进效果:")
        print(f"✅ 添加了强引用备份，防止垃圾回收")
        print(f"✅ 添加了弱引用监控，检测意外删除")
        print(f"✅ 添加了健康检查机制，定期验证状态")
        print(f"✅ 添加了多层恢复策略，确保可靠性")
    else:
        print(f"💥 部分测试失败，需要进一步调试")


if __name__ == "__main__":
    main()
