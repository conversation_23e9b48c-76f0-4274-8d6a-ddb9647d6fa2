#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传优化功能测试运行脚本
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_venv():
    """检查是否在虚拟环境中"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✓ 检测到虚拟环境")
        return True
    else:
        print("⚠ 未检测到虚拟环境，建议激活venv")
        return False

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['requests', 'loguru', 'fastapi', 'pydantic']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"✗ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✓ 所有依赖包已安装")
        return True

def run_unit_tests():
    """运行单元测试"""
    print("\n" + "=" * 60)
    print("运行单元测试")
    print("=" * 60)
    
    test_file = project_root / "tests" / "test_file_service_rag_status.py"
    
    try:
        # 激活虚拟环境并运行测试
        if (project_root / "venv" / "bin" / "python").exists():
            python_cmd = str(project_root / "venv" / "bin" / "python")
        elif (project_root / "venv" / "Scripts" / "python.exe").exists():
            python_cmd = str(project_root / "venv" / "Scripts" / "python.exe")
        else:
            python_cmd = sys.executable
        
        result = subprocess.run([python_cmd, str(test_file)], 
                              capture_output=True, text=True, cwd=str(project_root))
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"✗ 运行单元测试失败: {e}")
        return False

def run_integration_tests():
    """运行集成测试"""
    print("\n" + "=" * 60)
    print("运行集成测试")
    print("=" * 60)
    
    test_file = project_root / "tests" / "test_file_upload_optimization.py"
    
    try:
        # 激活虚拟环境并运行测试
        if (project_root / "venv" / "bin" / "python").exists():
            python_cmd = str(project_root / "venv" / "bin" / "python")
        elif (project_root / "venv" / "Scripts" / "python.exe").exists():
            python_cmd = str(project_root / "venv" / "Scripts" / "python.exe")
        else:
            python_cmd = sys.executable
        
        result = subprocess.run([python_cmd, str(test_file)], 
                              capture_output=True, text=True, cwd=str(project_root))
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"✗ 运行集成测试失败: {e}")
        return False

def check_service_status():
    """检查服务状态"""
    print("\n" + "=" * 60)
    print("检查服务状态")
    print("=" * 60)
    
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✓ 服务运行正常")
            return True
        else:
            print(f"⚠ 服务状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        print("请确保服务正在运行在 http://localhost:8000")
        return False

def show_optimization_summary():
    """显示优化总结"""
    print("\n" + "=" * 60)
    print("文件上传优化总结")
    print("=" * 60)
    
    print("✓ 优化内容:")
    print("  1. 修改 confirm-upload 接口，立即返回不等待RAG解析")
    print("  2. 新增 rag-status 接口，支持前端轮询查询处理进度")
    print("  3. 优化异步任务处理机制，使用线程池真正异步执行")
    print("  4. 完善状态管理和错误处理")
    
    print("\n✓ 接口变更:")
    print("  - POST /api/files/confirm-upload")
    print("    响应时间从几十秒优化到秒级返回")
    print("    返回状态: analyzing (表示RAG解析已启动)")
    print("  - POST /api/files/rag-status (新增)")
    print("    查询RAG解析进度和状态")
    print("    支持轮询调用")
    
    print("\n✓ 前端使用方式:")
    print("  1. 调用 confirm-upload 接口确认上传")
    print("  2. 立即收到响应，获得 file_id")
    print("  3. 使用 file_id 轮询 rag-status 接口")
    print("  4. 根据返回的 rag_status 判断处理状态:")
    print("     - waiting: 等待上传完成")
    print("     - analyzing: 正在RAG解析")
    print("     - completed: 解析完成")
    print("     - failed: 解析失败")

def main():
    """主函数"""
    print("=" * 60)
    print("文件上传优化功能测试")
    print("=" * 60)
    
    # 检查环境
    check_venv()
    
    if not check_dependencies():
        return
    
    # 显示优化总结
    show_optimization_summary()
    
    # 运行单元测试
    unit_test_success = run_unit_tests()
    
    # 检查服务状态
    service_running = check_service_status()
    
    # 如果服务运行正常，运行集成测试
    integration_test_success = False
    if service_running:
        integration_test_success = run_integration_tests()
    else:
        print("\n⚠ 跳过集成测试（服务未运行）")
        print("要运行集成测试，请先启动服务:")
        print("  cd " + str(project_root))
        print("  source venv/bin/activate  # Linux/Mac")
        print("  # 或 venv\\Scripts\\activate  # Windows")
        print("  python start_service.py")
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    print(f"单元测试: {'✓ 通过' if unit_test_success else '✗ 失败'}")
    print(f"服务状态: {'✓ 正常' if service_running else '✗ 异常'}")
    print(f"集成测试: {'✓ 通过' if integration_test_success else '✗ 失败或跳过'}")
    
    if unit_test_success and (not service_running or integration_test_success):
        print("\n🎉 文件上传优化功能测试完成！")
    else:
        print("\n⚠ 部分测试未通过，请检查相关问题")
    
    print("\n📝 使用说明:")
    print("1. 确保服务正在运行")
    print("2. 前端调用优化后的接口")
    print("3. 使用新的状态查询接口获取处理进度")
    print("4. 根据业务需求调整轮询间隔")

if __name__ == "__main__":
    main()
