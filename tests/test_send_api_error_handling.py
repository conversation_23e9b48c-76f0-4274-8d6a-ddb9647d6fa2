#!/usr/bin/env python3
"""
测试 /sessions/send 接口的错误处理机制
验证文件内容超长时直接返回 handle_exception 结果
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.session_service import FileContentTooLargeError
from src.application.api_models import ResourceType, SessionResource, SendMessageRequest
from src.domain.services.auth_service import AuthContext


class MockWaiyResource:
    """模拟WaiyInfra资源对象"""
    def __init__(self, resource_type: str, content: str = None):
        self.type = resource_type
        self.content = content


async def test_send_api_direct_error_handling():
    """测试 /send 接口直接返回 handle_exception 结果"""
    print("🧪 测试 /send 接口直接错误处理")
    print("=" * 60)
    
    # 导入必要的模块
    from src.presentation.api.routes.session_routes import send_message
    from src.domain.services.session_service import session_service
    from src.presentation.api.dependencies.api_common_utils import handle_exception
    
    # 创建认证上下文
    context = AuthContext(ali_uid=12345, wy_id="test_wy_id")
    
    # 测试用例1: 文件内容超限 - 验证直接返回错误响应
    print("\n=== 测试用例1: 文件内容超限直接返回错误 ===")
    try:
        # 模拟大文件内容（25万字符）
        large_content = "b" * 250000
        mock_large_resource = MockWaiyResource("file", large_content)
        
        # 创建请求
        request = SendMessageRequest(
            prompt="测试消息",
            agent_id="test_agent",
            resources=[
                SessionResource(type=ResourceType.FILE, resource_id="large_file")
            ]
        )
        
        # 模拟依赖
        with patch.object(session_service, '_process_file_resource', return_value=mock_large_resource), \
             patch.object(session_service, 'create_session', return_value="test_session_id"), \
             patch.object(session_service, 'check_session_permission'), \
             patch('src.presentation.api.routes.session_routes.get_request_id_dependency', return_value="test_request_123"):
            
            # 模拟FastAPI的Request对象
            mock_request = Mock()
            mock_request.headers = {}
            
            # 调用API接口
            result = await send_message(
                request=request,
                current_user=context,
                common_params=Mock(),
                request_id="test_request_123"
            )
            
            print("✅ API调用成功，检查返回结果:")
            print(f"   返回类型: {type(result)}")
            print(f"   返回内容: {result}")
            
            # 验证返回结果格式
            if isinstance(result, dict):
                print(f"   错误码: {result.get('code')}")
                print(f"   错误消息: {result.get('message')}")
                print(f"   成功标志: {result.get('success')}")
                print(f"   请求ID: {result.get('request_id')}")
                
                # 验证是否是预期的错误响应
                if (result.get('code') == 'FILE_CONTENT_TOO_LARGE' and 
                    result.get('message') == '文件内容过多，请精简一下' and
                    result.get('success') is False):
                    print("✅ 错误处理正确：返回了预期的文件内容过大错误")
                else:
                    print("❌ 错误处理不正确：返回格式不符合预期")
            else:
                print("❌ 返回类型不正确：应该返回字典格式的错误响应")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
    
    # 测试用例2: 正常情况 - 验证正常流程不受影响
    print("\n=== 测试用例2: 正常情况验证 ===")
    try:
        # 模拟小文件内容（5万字符）
        small_content = "a" * 50000
        mock_small_resource = MockWaiyResource("file", small_content)
        
        # 创建请求
        request = SendMessageRequest(
            prompt="测试消息",
            agent_id="test_agent",
            resources=[
                SessionResource(type=ResourceType.FILE, resource_id="small_file")
            ]
        )
        
        # 模拟依赖
        with patch.object(session_service, '_process_file_resource', return_value=mock_small_resource), \
             patch.object(session_service, 'create_session', return_value="test_session_id"), \
             patch.object(session_service, 'check_session_permission'), \
             patch('src.presentation.api.routes.session_routes.get_request_id_dependency', return_value="test_request_456"), \
             patch('asyncio.create_task') as mock_create_task:
            
            # 模拟异步任务
            mock_task = Mock()
            mock_create_task.return_value = mock_task
            
            # 调用API接口
            result = await send_message(
                request=request,
                current_user=context,
                common_params=Mock(),
                request_id="test_request_456"
            )
            
            print("✅ 正常情况API调用成功:")
            print(f"   返回类型: {type(result)}")
            print(f"   返回内容: {result}")
            
            # 验证返回结果格式
            if isinstance(result, dict):
                if result.get('success') is True and 'session_id' in result.get('data', {}):
                    print("✅ 正常流程正确：返回了成功的会话创建响应")
                else:
                    print("❌ 正常流程异常：返回格式不符合预期")
            else:
                print("❌ 返回类型不正确：应该返回字典格式的成功响应")
                
    except Exception as e:
        print(f"❌ 正常情况测试异常: {e}")


async def test_handle_exception_function():
    """测试 handle_exception 函数本身"""
    print("\n=== 测试 handle_exception 函数 ===")
    
    from src.presentation.api.dependencies.api_common_utils import handle_exception
    from src.domain.services.session_service import FileContentTooLargeError
    
    # 测试FileContentTooLargeError异常处理
    try:
        error = FileContentTooLargeError(
            message="文件内容过多，请精简一下",
            total_length=300000,
            limit=200000
        )
        
        result = handle_exception(error, "test_request_789")
        
        print("✅ handle_exception 函数测试通过:")
        print(f"   返回结构: {result}")
        
        # 验证返回格式
        expected_fields = ['code', 'message', 'success', 'request_id', 'data', 'status']
        missing_fields = [field for field in expected_fields if field not in result]
        
        if not missing_fields:
            print("✅ 返回字段完整")
        else:
            print(f"❌ 缺少字段: {missing_fields}")
            
        # 验证具体值
        checks = [
            (result.get('code') == 'FILE_CONTENT_TOO_LARGE', "错误码正确"),
            (result.get('message') == '文件内容过多，请精简一下', "错误消息正确"),
            (result.get('success') is False, "成功标志正确"),
            (result.get('request_id') == 'test_request_789', "请求ID正确"),
            (result.get('status') == 400, "HTTP状态码正确")
        ]
        
        for check, desc in checks:
            if check:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                
    except Exception as e:
        print(f"❌ handle_exception 函数测试失败: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始测试 /sessions/send 接口错误处理机制")
    
    await test_send_api_direct_error_handling()
    await test_handle_exception_function()
    
    print("\n" + "=" * 60)
    print("✅ 测试完成！")
    
    print("\n📋 修改总结:")
    print("1. ✅ 修改了 /sessions/send 接口的异常处理逻辑")
    print("2. ✅ 文件内容超长时直接返回 handle_exception(resource_error, request_id)")
    print("3. ✅ 不再抛出异常到外层，避免了异常传播")
    print("4. ✅ 保持了统一的错误响应格式")
    
    print("\n🎯 用户体验:")
    print("- 文件内容过大时立即返回友好的错误信息")
    print("- 错误响应格式统一，便于前端处理")
    print("- 不会创建无效的会话，节省资源")


if __name__ == "__main__":
    asyncio.run(main())
