#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 MemorySDK 事件循环优化
验证使用 asyncio.run() 替代手动创建事件循环的效果
"""

import asyncio
import time
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from memory.events import Event, EventType


class MockEvent:
    """模拟Event对象"""
    def __init__(self):
        self.event_id = "test_event"
        self.type = EventType.TEXT_MESSAGE_CONTENT
        self.session_id = "test_session"
        self.run_id = "test_run"
        self.content = "test content"


async def mock_callback(event):
    """模拟异步回调函数"""
    await asyncio.sleep(0.01)  # 模拟一些异步工作
    print(f"处理事件: {event.event_id}")


def test_old_approach():
    """测试旧的事件循环创建方式"""
    print("=== 测试旧的事件循环创建方式 ===")
    
    start_time = time.time()
    
    for i in range(10):
        event = MockEvent()
        event.event_id = f"old_event_{i}"
        
        # 旧的方式：每次创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(mock_callback(event))
        finally:
            loop.close()
    
    end_time = time.time()
    print(f"旧方式处理10个事件耗时: {end_time - start_time:.4f}秒")


def test_new_approach():
    """测试新的 asyncio.run() 方式"""
    print("\n=== 测试新的 asyncio.run() 方式 ===")
    
    start_time = time.time()
    
    for i in range(10):
        event = MockEvent()
        event.event_id = f"new_event_{i}"
        
        # 新的方式：使用 asyncio.run()
        asyncio.run(mock_callback(event))
    
    end_time = time.time()
    print(f"新方式处理10个事件耗时: {end_time - start_time:.4f}秒")


def test_memory_sdk_integration():
    """测试与 MemorySDK 的集成"""
    print("\n=== 测试与 MemorySDK 的集成 ===")
    
    # 模拟 MemorySDK 的回调处理
    callbacks = {
        "on_new_message": mock_callback
    }
    
    def handle_new_message_sync_old(event):
        """旧的同步处理方式"""
        if "on_new_message" in callbacks:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(callbacks["on_new_message"](event))
            finally:
                loop.close()
    
    def handle_new_message_sync_new(event):
        """新的同步处理方式"""
        if "on_new_message" in callbacks:
            asyncio.run(callbacks["on_new_message"](event))
    
    # 测试旧方式
    start_time = time.time()
    for i in range(5):
        event = MockEvent()
        event.event_id = f"integration_old_{i}"
        handle_new_message_sync_old(event)
    old_time = time.time() - start_time
    
    # 测试新方式
    start_time = time.time()
    for i in range(5):
        event = MockEvent()
        event.event_id = f"integration_new_{i}"
        handle_new_message_sync_new(event)
    new_time = time.time() - start_time
    
    print(f"集成测试 - 旧方式耗时: {old_time:.4f}秒")
    print(f"集成测试 - 新方式耗时: {new_time:.4f}秒")
    print(f"性能提升: {((old_time - new_time) / old_time * 100):.1f}%")


if __name__ == "__main__":
    print("开始测试 MemorySDK 事件循环优化")
    
    test_old_approach()
    test_new_approach()
    test_memory_sdk_integration()
    
    print("\n🎉 测试完成！")
    print("\n📊 总结:")
    print("✅ asyncio.run() 方式更简洁")
    print("✅ 自动管理事件循环生命周期")
    print("✅ 减少资源泄漏风险")
    print("✅ 代码更易维护")
