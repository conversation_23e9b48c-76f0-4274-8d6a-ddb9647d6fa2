#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库文档预览API接口
"""

import requests
import json


def test_document_preview_api():
    """测试获取文档预览链接接口"""
    
    # 配置
    base_url = "http://localhost:8000"
    headers = {
        "Content-Type": "application/json",
        # 注意：这里需要根据实际的认证方式设置正确的headers
        # "Authorization": "Bearer your_token_here"
    }
    
    # 测试参数
    file_id = "test_file_001"
    
    # 构造请求
    url = f"{base_url}/api/knowledge_base/document/preview"
    request_data = {
        "file_id": file_id
    }
    
    try:
        print("=" * 60)
        print("测试知识库文档预览API接口")
        print("=" * 60)
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        # 发送请求
        response = requests.post(url, json=request_data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            # 解析响应
            response_data = response.json()
            
            if response_data.get("success"):
                preview_data = response_data.get("data", {})
                print(f"\n✅ 成功获取预览链接:")
                print(f"  文件ID: {preview_data.get('file_id')}")
                print(f"  文件名: {preview_data.get('file_name')}")
                print(f"  预览链接: {preview_data.get('preview_url')}")
                print(f"  过期时间: {preview_data.get('expires_in')}秒")
                
                # 验证返回数据结构
                required_fields = ["file_id", "file_name", "preview_url", "expires_in"]
                missing_fields = [field for field in required_fields if field not in preview_data]
                
                if missing_fields:
                    print(f"\n❌ 响应数据缺少字段: {missing_fields}")
                else:
                    print(f"\n✅ 响应数据结构验证通过")
                    
                    # 验证字段类型
                    if (isinstance(preview_data.get("file_id"), str) and
                        isinstance(preview_data.get("file_name"), str) and
                        isinstance(preview_data.get("preview_url"), str) and
                        isinstance(preview_data.get("expires_in"), int)):
                        print("✅ 字段类型验证通过")
                    else:
                        print("❌ 字段类型验证失败")
            else:
                print(f"\n❌ 请求失败: {response_data.get('message', '未知错误')}")
        else:
            print(f"\n❌ HTTP请求失败，状态码: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 网络请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"\n❌ JSON解析失败: {e}")
    except Exception as e:
        print(f"\n❌ 其他异常: {e}")
    
    print("\n" + "=" * 60)


def test_invalid_params():
    """测试无效参数"""
    base_url = "http://localhost:8000"
    url = f"{base_url}/api/knowledge_base/document/preview"
    headers = {"Content-Type": "application/json"}
    
    test_cases = [
        {"file_id": "", "desc": "空的file_id"},
        {"file_id": "   ", "desc": "空白字符的file_id"},
        {}, {"desc": "缺少file_id参数"},
        {"file_id": None, "desc": "file_id为None"},
    ]
    
    print("测试无效参数情况:")
    print("-" * 40)
    
    for case in test_cases:
        request_data = {k: v for k, v in case.items() if k != "desc"}
        try:
            response = requests.post(url, json=request_data, headers=headers)
            print(f"{case.get('desc', '未知测试')}: 状态码 {response.status_code}")
            
            if response.status_code != 200:
                try:
                    error_data = response.json()
                    print(f"  错误信息: {error_data.get('message', '无错误信息')}")
                except:
                    print(f"  响应内容: {response.text[:100]}...")
        except Exception as e:
            print(f"{case.get('desc', '未知测试')}: 异常 {e}")


def test_nonexistent_file():
    """测试不存在的文件"""
    base_url = "http://localhost:8000"
    url = f"{base_url}/api/knowledge_base/document/preview"
    headers = {"Content-Type": "application/json"}
    
    # 使用一个不存在的file_id
    request_data = {"file_id": "nonexistent_file_12345"}
    
    print("\n测试不存在的文件:")
    print("-" * 40)
    
    try:
        response = requests.post(url, json=request_data, headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            if not response_data.get("success"):
                print(f"✅ 正确返回失败: {response_data.get('message')}")
            else:
                print("❌ 应该返回失败但返回了成功")
        else:
            print(f"状态码: {response.status_code}")
            
    except Exception as e:
        print(f"异常: {e}")


if __name__ == "__main__":
    # 测试正常情况
    test_document_preview_api()
    
    # 测试异常情况
    test_invalid_params()
    
    # 测试不存在的文件
    test_nonexistent_file()
