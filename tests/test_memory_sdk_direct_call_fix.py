#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 MemorySDK 直接调用修复
验证异步方法调用和避免重复处理的修复
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from memory.events import Event, EventType


class MockEvent:
    """模拟Event对象"""
    def __init__(self):
        self.event_id = "test_event"
        self.type = EventType.TEXT_MESSAGE_CONTENT
        self.session_id = "test_session"
        self.run_id = "test_run"
        self.content = "test content"

    def __str__(self):
        return f"MockEvent(id={self.event_id}, type={self.type})"


class MockMessageProcessor:
    """模拟MessageProcessor"""
    def __init__(self):
        self.call_count = 0
        self.events_received = []

    async def handle_new_message(self, event):
        """模拟异步处理方法"""
        self.call_count += 1
        self.events_received.append(event)
        print(f"handle_new_message: {event} (调用次数: {self.call_count})")
        await asyncio.sleep(0.01)  # 模拟异步处理


def test_direct_async_call():
    """测试直接异步调用"""
    print("=== 测试直接异步调用 ===")
    
    processor = MockMessageProcessor()
    event = MockEvent()
    
    # 模拟 MemorySDK 的直接调用方式
    def on_mq_message_received(event):
        try:
            print(f"收到新的event:{event}")
            # 使用 asyncio.run 调用异步方法
            asyncio.run(processor.handle_new_message(event))
        except Exception as e:
            print(f"处理MQ消息失败: {e}")
    
    # 执行测试
    on_mq_message_received(event)
    
    # 验证结果
    assert processor.call_count == 1, f"期望调用1次，实际调用{processor.call_count}次"
    assert len(processor.events_received) == 1, f"期望接收1个事件，实际接收{len(processor.events_received)}个"
    print("✅ 直接异步调用测试通过")


def test_wrong_sync_call():
    """测试错误的同步调用（演示问题）"""
    print("\n=== 测试错误的同步调用（演示问题）===")
    
    processor = MockMessageProcessor()
    event = MockEvent()
    
    # 模拟错误的调用方式（不使用 asyncio.run）
    def wrong_on_mq_message_received(event):
        try:
            print(f"收到新的event:{event}")
            # 错误：直接调用异步方法但不 await
            result = processor.handle_new_message(event)  # 这只会返回 coroutine 对象
            print(f"返回结果类型: {type(result)}")
            print(f"是否为 coroutine: {asyncio.iscoroutine(result)}")
        except Exception as e:
            print(f"处理MQ消息失败: {e}")
    
    # 执行测试
    wrong_on_mq_message_received(event)
    
    # 验证结果
    assert processor.call_count == 0, f"期望调用0次（因为没有真正执行），实际调用{processor.call_count}次"
    assert len(processor.events_received) == 0, f"期望接收0个事件，实际接收{len(processor.events_received)}个"
    print("✅ 错误调用演示完成 - 异步方法确实没有被执行")


def test_no_duplicate_processing():
    """测试避免重复处理"""
    print("\n=== 测试避免重复处理 ===")
    
    processor = MockMessageProcessor()
    event = MockEvent()
    
    # 模拟修复后的单一调用路径
    def fixed_on_mq_message_received(event):
        try:
            print(f"收到新的event:{event}")
            # 只有一个调用路径：直接调用
            asyncio.run(processor.handle_new_message(event))
        except Exception as e:
            print(f"处理MQ消息失败: {e}")
    
    # 执行多次测试，确保每次只处理一次
    for i in range(3):
        fixed_on_mq_message_received(event)
    
    # 验证结果
    assert processor.call_count == 3, f"期望调用3次，实际调用{processor.call_count}次"
    assert len(processor.events_received) == 3, f"期望接收3个事件，实际接收{len(processor.events_received)}个"
    print("✅ 避免重复处理测试通过")


def test_memory_sdk_integration():
    """测试 MemorySDK 集成"""
    print("\n=== 测试 MemorySDK 集成 ===")
    
    # 模拟 MemorySDK 的结构
    class MockMemorySDK:
        def __init__(self):
            self.message_processor = MockMessageProcessor()
            self._callbacks = {}
        
        def _on_mq_message_received(self, event):
            """修复后的消息接收处理"""
            try:
                print(f"收到新的event:{event}")
                # 直接调用 MessageProcessor
                asyncio.run(self.message_processor.handle_new_message(event))
            except Exception as e:
                print(f"[MemorySDK] 处理MQ消息失败: {e}")
        
        def register_callback(self, callback_name, callback_func):
            """注册回调（现在不再使用）"""
            self._callbacks[callback_name] = callback_func
            print(f"[MemorySDK] 注册回调: {callback_name}")
    
    # 创建 MemorySDK 实例
    memory_sdk = MockMemorySDK()
    event = MockEvent()
    
    # 测试直接调用路径
    memory_sdk._on_mq_message_received(event)
    
    # 验证结果
    processor = memory_sdk.message_processor
    assert processor.call_count == 1, f"期望调用1次，实际调用{processor.call_count}次"
    assert len(processor.events_received) == 1, f"期望接收1个事件，实际接收{len(processor.events_received)}个"
    print("✅ MemorySDK 集成测试通过")


def test_performance_comparison():
    """测试性能对比"""
    print("\n=== 测试性能对比 ===")
    
    import time
    
    processor = MockMessageProcessor()
    event = MockEvent()
    
    # 测试直接调用的性能
    start_time = time.time()
    for i in range(10):
        asyncio.run(processor.handle_new_message(event))
    direct_time = time.time() - start_time
    
    print(f"直接调用10次耗时: {direct_time:.4f}秒")
    print(f"平均每次耗时: {direct_time/10:.4f}秒")
    
    # 重置计数器
    processor.call_count = 0
    processor.events_received = []
    
    # 验证所有调用都成功执行
    assert processor.call_count == 0, "计数器应该被重置"
    
    # 再次测试确保一致性
    for i in range(5):
        asyncio.run(processor.handle_new_message(event))
    
    assert processor.call_count == 5, f"期望调用5次，实际调用{processor.call_count}次"
    print("✅ 性能测试完成")


if __name__ == "__main__":
    print("开始测试 MemorySDK 直接调用修复")
    
    test_direct_async_call()
    test_wrong_sync_call()
    test_no_duplicate_processing()
    test_memory_sdk_integration()
    test_performance_comparison()
    
    print("\n🎉 所有测试通过！")
    print("\n📋 修复总结:")
    print("✅ 修复了异步方法调用问题（使用 asyncio.run）")
    print("✅ 避免了消息重复处理")
    print("✅ 简化了调用链路")
    print("✅ 提高了代码可维护性")
    print("\n🔧 关键修复点:")
    print("1. 在同步上下文中使用 asyncio.run() 调用异步方法")
    print("2. 移除了重复的回调机制")
    print("3. 确保每个消息只被处理一次")
