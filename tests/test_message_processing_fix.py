#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试消息处理修复
验证消息不会被跳过，能正常处理
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.message_processor import MessageProcessor
from memory.events import Event, EventType


class MockEvent:
    """模拟Event对象"""
    def __init__(self, event_type=EventType.TEXT_MESSAGE_CONTENT):
        self.event_id = "test_event"
        self.type = event_type
        self.session_id = "test_session"
        self.run_id = "test_run"
        self.content = "test content"
        self.role = "assistant"
        self.message_id = "test_message"

    def __str__(self):
        return f"MockEvent(id={self.event_id}, type={self.type})"


class MockSession:
    """模拟Session对象"""
    def __init__(self):
        self.session_id = "test_session"
        self.status = "PROCESSING"

    def finish_processing(self):
        self.status = "CLOSED"


class MockSSEManager:
    """模拟SSE管理器"""
    def __init__(self, has_connection=False):
        self.sse_connections = {}
        if has_connection:
            self.sse_connections["test_session"] = Mock()
            
    async def push_to_sse(self, session_id, round_id, data, event_type="message"):
        print(f"[MockSSEManager] 推送消息: session_id={session_id}, event_type={event_type}")
        
    def close_session_connection(self, session_id):
        print(f"[MockSSEManager] 关闭连接: session_id={session_id}")


async def test_normal_message_without_sse():
    """测试没有SSE连接时的普通消息处理"""
    print("=== 测试没有SSE连接时的普通消息处理 ===")
    
    processor = MessageProcessor()
    processor.set_sse_manager(MockSSEManager(has_connection=False))
    
    event = MockEvent(EventType.TEXT_MESSAGE_CONTENT)
    mock_session = MockSession()
    
    with patch.object(processor, '_load_session_from_db', return_value=mock_session):
        with patch.object(processor, '_serialize_event_for_sse', return_value={}):
            
            # 执行消息处理
            await processor._handle_new_message_internal("test_session", "test_run", event)
            
            print("✅ 普通消息处理完成（没有SSE连接时会跳过）")


async def test_normal_message_with_sse():
    """测试有SSE连接时的普通消息处理"""
    print("\n=== 测试有SSE连接时的普通消息处理 ===")
    
    processor = MessageProcessor()
    sse_manager = MockSSEManager(has_connection=True)
    processor.set_sse_manager(sse_manager)
    
    event = MockEvent(EventType.TEXT_MESSAGE_CONTENT)
    mock_session = MockSession()
    
    with patch.object(processor, '_load_session_from_db', return_value=mock_session):
        with patch.object(processor, '_serialize_event_for_sse', return_value={"test": "data"}):
            with patch.object(sse_manager, 'push_to_sse', new_callable=AsyncMock) as mock_push:
                
                # 执行消息处理
                await processor._handle_new_message_internal("test_session", "test_run", event)
                
                # 验证SSE推送被调用
                mock_push.assert_called_once()
                print("✅ 有SSE连接时，普通消息正常推送")


async def test_run_finished_without_sse():
    """测试没有SSE连接时的RUN_FINISHED事件处理"""
    print("\n=== 测试没有SSE连接时的RUN_FINISHED事件处理 ===")
    
    processor = MessageProcessor()
    processor.set_sse_manager(MockSSEManager(has_connection=False))
    
    event = MockEvent(EventType.RUN_FINISHED)
    mock_session = MockSession()
    
    with patch.object(processor, '_load_session_from_db', return_value=mock_session):
        with patch.object(processor, '_serialize_event_for_sse', return_value={}):
            
            # 执行消息处理
            await processor._handle_new_message_internal("test_session", "test_run", event)
            
            # 验证Session状态被更新
            assert mock_session.status == "CLOSED", f"期望状态为CLOSED，实际为{mock_session.status}"
            print("✅ 没有SSE连接时，RUN_FINISHED事件仍能正确处理Session状态")


async def test_run_finished_with_sse():
    """测试有SSE连接时的RUN_FINISHED事件处理"""
    print("\n=== 测试有SSE连接时的RUN_FINISHED事件处理 ===")
    
    processor = MessageProcessor()
    sse_manager = MockSSEManager(has_connection=True)
    processor.set_sse_manager(sse_manager)
    
    event = MockEvent(EventType.RUN_FINISHED)
    mock_session = MockSession()
    
    with patch.object(processor, '_load_session_from_db', return_value=mock_session):
        with patch.object(processor, '_serialize_event_for_sse', return_value={}):
            with patch.object(processor, '_format_done_event_data', return_value={"type": "done"}):
                with patch.object(sse_manager, 'push_to_sse', new_callable=AsyncMock) as mock_push:
                    with patch.object(sse_manager, 'close_session_connection') as mock_close:
                        with patch('asyncio.sleep', new_callable=AsyncMock):
                            
                            # 执行消息处理
                            await processor._handle_new_message_internal("test_session", "test_run", event)
                            
                            # 验证Session状态被更新
                            assert mock_session.status == "CLOSED", f"期望状态为CLOSED，实际为{mock_session.status}"
                            
                            # 验证SSE推送被调用（普通消息 + done事件）
                            assert mock_push.call_count == 2, f"期望调用2次，实际调用{mock_push.call_count}次"
                            
                            # 验证连接被关闭
                            mock_close.assert_called_once()
                            
                            print("✅ 有SSE连接时，RUN_FINISHED事件正确处理并发送done事件")


async def test_message_processing_flow():
    """测试完整的消息处理流程"""
    print("\n=== 测试完整的消息处理流程 ===")
    
    processor = MessageProcessor()
    
    # 测试不同类型的事件
    test_cases = [
        (EventType.TEXT_MESSAGE_CONTENT, "普通文本消息"),
        (EventType.TEXT_MESSAGE_DELTA_CONTENT, "增量文本消息"),
        (EventType.RUN_FINISHED, "运行完成事件"),
        (EventType.RUN_ERROR, "运行错误事件"),
    ]
    
    for event_type, description in test_cases:
        print(f"\n测试 {description}:")
        
        # 测试没有SSE连接的情况
        processor.set_sse_manager(MockSSEManager(has_connection=False))
        event = MockEvent(event_type)
        mock_session = MockSession()
        
        with patch.object(processor, '_load_session_from_db', return_value=mock_session):
            with patch.object(processor, '_serialize_event_for_sse', return_value={}):
                with patch.object(processor, '_format_done_event_data', return_value={"type": "done"}):
                    
                    try:
                        await processor._handle_new_message_internal("test_session", "test_run", event)
                        
                        if event_type in [EventType.RUN_FINISHED, EventType.RUN_ERROR]:
                            assert mock_session.status == "CLOSED", f"{description}: Session状态应该为CLOSED"
                            print(f"  ✅ {description}: 会话完成事件正确处理")
                        else:
                            print(f"  ✅ {description}: 普通消息被跳过（符合预期）")
                            
                    except Exception as e:
                        print(f"  ❌ {description}: 处理失败 - {e}")


if __name__ == "__main__":
    print("开始测试消息处理修复")
    
    async def run_all_tests():
        await test_normal_message_without_sse()
        await test_normal_message_with_sse()
        await test_run_finished_without_sse()
        await test_run_finished_with_sse()
        await test_message_processing_flow()
    
    asyncio.run(run_all_tests())
    
    print("\n🎉 所有测试完成！")
    print("\n📋 修复效果:")
    print("✅ 普通消息：没有SSE连接时会跳过，有连接时正常处理")
    print("✅ 完成事件：无论是否有SSE连接都会处理Session状态")
    print("✅ 避免了消息处理被意外跳过的问题")
    print("✅ 确保会话状态的一致性")
