#!/usr/bin/env python3
"""
测试文件处理错误处理机制
"""

import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_custom_exceptions():
    """测试自定义异常类"""
    print("🧪 测试自定义异常类")
    print("=" * 50)
    
    try:
        from src.domain.services.file_service import (
            FileProcessingFailedException, 
            FileProcessingTimeoutException
        )
        
        # 测试FileProcessingFailedException
        failed_exception = FileProcessingFailedException(
            message="文档解析失败",
            file_id=123,
            doc_id="doc-456"
        )
        
        print("✅ FileProcessingFailedException 创建成功:")
        print(f"   消息: {failed_exception.message}")
        print(f"   文件ID: {failed_exception.file_id}")
        print(f"   文档ID: {failed_exception.doc_id}")
        print(f"   字符串表示: {str(failed_exception)}")
        
        # 测试FileProcessingTimeoutException
        timeout_exception = FileProcessingTimeoutException(
            message="文档处理超时",
            file_id=789,
            doc_id="doc-101112",
            timeout_seconds=30.5
        )
        
        print("\n✅ FileProcessingTimeoutException 创建成功:")
        print(f"   消息: {timeout_exception.message}")
        print(f"   文件ID: {timeout_exception.file_id}")
        print(f"   文档ID: {timeout_exception.doc_id}")
        print(f"   超时时间: {timeout_exception.timeout_seconds}秒")
        print(f"   字符串表示: {str(timeout_exception)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义异常类测试失败: {e}")
        return False


def test_api_exception_handling():
    """测试API异常处理"""
    print("\n🧪 测试API异常处理")
    print("=" * 50)
    
    try:
        from src.presentation.api.dependencies.api_common_utils import handle_exception
        from src.domain.services.file_service import (
            FileProcessingFailedException, 
            FileProcessingTimeoutException
        )
        
        # 测试FileProcessingFailedException处理
        failed_exception = FileProcessingFailedException(
            message="文档解析失败",
            file_id=123,
            doc_id="doc-456"
        )
        
        result = handle_exception(failed_exception, "test_request_123")
        
        print("✅ FileProcessingFailedException 处理测试:")
        print(f"   返回码: {result.get('code')}")
        print(f"   消息: {result.get('message')}")
        print(f"   成功标志: {result.get('success')}")
        print(f"   请求ID: {result.get('request_id')}")
        
        # 验证返回格式
        if (result.get('code') == 'FILE_PROCESSING_FAILED' and 
            result.get('message') == '文档解析失败' and
            result.get('success') is False):
            print("   ✅ 返回格式正确")
        else:
            print("   ❌ 返回格式不正确")
            return False
        
        # 测试FileProcessingTimeoutException处理
        timeout_exception = FileProcessingTimeoutException(
            message="文档处理超时",
            file_id=789,
            doc_id="doc-101112",
            timeout_seconds=30.5
        )
        
        result = handle_exception(timeout_exception, "test_request_456")
        
        print("\n✅ FileProcessingTimeoutException 处理测试:")
        print(f"   返回码: {result.get('code')}")
        print(f"   消息: {result.get('message')}")
        print(f"   成功标志: {result.get('success')}")
        print(f"   请求ID: {result.get('request_id')}")
        
        # 验证返回格式
        if (result.get('code') == 'FILE_PROCESSING_TIMEOUT' and 
            result.get('message') == '文档处理超时' and
            result.get('success') is False):
            print("   ✅ 返回格式正确")
        else:
            print("   ❌ 返回格式不正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API异常处理测试失败: {e}")
        import traceback
        print(f"   异常堆栈: {traceback.format_exc()}")
        return False


def test_status_check_logic():
    """测试状态检查逻辑"""
    print("\n🧪 测试状态检查逻辑")
    print("=" * 50)
    
    try:
        # 模拟响应数据结构
        class MockInfos:
            def __init__(self, final_state):
                self.final_state = final_state
        
        class MockDocInfo:
            def __init__(self, final_state, stages=None):
                self.infos = MockInfos(final_state)
                self.stages = stages or []
        
        # 测试failed状态检查
        print("📝 测试failed状态检查:")
        
        # 模拟failed状态的doc_info
        failed_doc_info = MockDocInfo(final_state='failed')
        
        # 检查final_state逻辑
        if hasattr(failed_doc_info, 'infos') and failed_doc_info.infos:
            infos = failed_doc_info.infos
            final_state = getattr(infos, 'final_state', None)
            
            print(f"   final_state: {final_state}")
            
            if final_state == 'failed':
                print("   ✅ 正确识别failed状态")
            else:
                print("   ❌ 未能识别failed状态")
                return False
        
        # 测试processing状态检查
        print("\n📝 测试processing状态检查:")
        
        processing_doc_info = MockDocInfo(
            final_state='processing', 
            stages=['raw', 'markdown_parse', 'chunk_parse']
        )
        
        if hasattr(processing_doc_info, 'infos') and processing_doc_info.infos:
            infos = processing_doc_info.infos
            final_state = getattr(infos, 'final_state', None)
            
            print(f"   final_state: {final_state}")
            
            if final_state == 'processing':
                print("   ✅ 正确识别processing状态")
                
                # 检查stages
                if hasattr(processing_doc_info, 'stages') and processing_doc_info.stages:
                    stages = processing_doc_info.stages
                    print(f"   stages: {stages}")
                    
                    if 'chunk_parse' in stages:
                        print("   ✅ 正确识别chunk_parse完成")
                    else:
                        print("   ℹ️ chunk_parse未完成")
            else:
                print("   ❌ 未能识别processing状态")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 状态检查逻辑测试失败: {e}")
        return False


def test_code_modifications():
    """测试代码修改"""
    print("\n🧪 测试代码修改")
    print("=" * 50)
    
    try:
        # 检查file_service.py中的修改
        file_service_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'domain', 'services', 'file_service.py')
        
        with open(file_service_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查异常类定义
        checks = [
            ('class FileProcessingFailedException(Exception):', "FileProcessingFailedException异常类定义"),
            ('class FileProcessingTimeoutException(Exception):', "FileProcessingTimeoutException异常类定义"),
            ('final_state = getattr(infos, \'final_state\', None)', "final_state状态检查"),
            ('if final_state == \'failed\':', "failed状态判断"),
            ('raise FileProcessingFailedException', "FileProcessingFailedException抛出"),
            ('raise FileProcessingTimeoutException', "FileProcessingTimeoutException抛出"),
            ('self.file_repository.mark_file_failed(file_id, "文档解析失败")', "文档解析失败状态标记"),
            ('self.file_repository.mark_file_failed(file_id, "文档处理超时")', "文档处理超时状态标记")
        ]
        
        print("📝 file_service.py 修改检查:")
        all_passed = True
        for check, desc in checks:
            if check in content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        # 检查file_routes.py中的修改
        file_routes_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'routes', 'file_routes.py')
        
        with open(file_routes_path, 'r', encoding='utf-8') as f:
            routes_content = f.read()
        
        print("\n📝 file_routes.py 修改检查:")
        if 'return handle_exception(e, request_id)' in routes_content:
            print("   ✅ 使用统一异常处理")
        else:
            print("   ❌ 未使用统一异常处理")
            all_passed = False
        
        # 检查api_common_utils.py中的修改
        api_utils_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'presentation', 'api', 'dependencies', 'api_common_utils.py')
        
        with open(api_utils_path, 'r', encoding='utf-8') as f:
            utils_content = f.read()
        
        print("\n📝 api_common_utils.py 修改检查:")
        utils_checks = [
            ('from src.domain.services.file_service import FileProcessingFailedException, FileProcessingTimeoutException', "异常类导入"),
            ('elif isinstance(e, FileProcessingFailedException):', "FileProcessingFailedException处理"),
            ('elif isinstance(e, FileProcessingTimeoutException):', "FileProcessingTimeoutException处理"),
            ('"FILE_PROCESSING_FAILED"', "FILE_PROCESSING_FAILED错误码"),
            ('"FILE_PROCESSING_TIMEOUT"', "FILE_PROCESSING_TIMEOUT错误码")
        ]
        
        for check, desc in utils_checks:
            if check in utils_content:
                print(f"   ✅ {desc}")
            else:
                print(f"   ❌ {desc}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 代码修改检查失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试文件处理错误处理机制")
    
    tests = [
        test_custom_exceptions,
        test_api_exception_handling,
        test_status_check_logic,
        test_code_modifications
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 异常: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print("✅ 所有测试通过！文件处理错误处理机制实现正确")
    else:
        print(f"❌ {total - passed} 个测试失败，{passed} 个测试通过")
    
    print(f"\n📊 测试结果: {passed}/{total}")
    
    print("\n📋 实现总结:")
    print("1. ✅ 创建了FileProcessingFailedException和FileProcessingTimeoutException异常类")
    print("2. ✅ 在文档处理监控中添加了final_state状态检查")
    print("3. ✅ failed状态时抛出FileProcessingFailedException异常")
    print("4. ✅ 超时时抛出FileProcessingTimeoutException异常")
    print("5. ✅ 异常抛出前先标记文件状态为failed")
    print("6. ✅ 在API层使用统一的异常处理机制")
    print("7. ✅ 在handle_exception中添加了新异常类型的处理")
    
    print("\n🎯 错误处理流程:")
    print("- 文档处理失败 → 标记文件状态为failed → 抛出FileProcessingFailedException")
    print("- 文档处理超时 → 标记文件状态为failed → 抛出FileProcessingTimeoutException")
    print("- API层捕获异常 → handle_exception处理 → 返回友好错误信息")
    print("- 前端收到具体的错误码和错误信息")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
