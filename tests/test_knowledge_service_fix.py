#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库服务中 _fill_log_data 方法的 KeyError 修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from unittest.mock import Mock, patch
from src.domain.services.knowledge_service import KnowledgeService
from src.domain.services.auth_service import AuthContext
from application.rag_api_models import KnowledgeBaseLogListResponse


class TestKnowledgeServiceFix:
    """测试知识库服务修复"""

    def setup_method(self):
        """设置测试环境"""
        self.knowledge_service = KnowledgeService()
        self.auth_context = AuthContext(
            ali_uid=12345,
            wy_id="test_wy_id"
        )

    def test_fill_log_data_with_missing_kb_id(self):
        """测试当日志中的知识库ID不存在时的处理"""
        
        # 模拟日志数据，包含一个不存在的知识库ID
        log_data = [
            Mock(
                kb_id="kb-existing-id",
                target_type="session",
                target_id="session-1"
            ),
            Mock(
                kb_id="kb-missing-id",  # 这个ID在知识库列表中不存在
                target_type="document", 
                target_id="doc-1"
            ),
            Mock(
                kb_id=None,  # 空的知识库ID
                target_type="session",
                target_id="session-2"
            )
        ]

        # 模拟知识库查询结果，只返回一个存在的知识库
        mock_kb = Mock()
        mock_kb.kb_id = "kb-existing-id"
        mock_kb.name = "测试知识库"

        # Mock repository 方法
        with patch.object(
            self.knowledge_service.knowledgebase_repository,
            'list_knowledge_bases',
            return_value=[mock_kb]
        ):
            with patch.object(
                self.knowledge_service.kb_sessions_repository,
                'list_sessions',
                return_value=[]
            ):
                with patch.object(
                    self.knowledge_service.kb_documents_repository,
                    'list_kb_documents',
                    return_value=[]
                ):
                    with patch('src.domain.services.session_service.session_service.get_sessions_by_ids', return_value=[]):
                        # 调用被测试的方法
                        try:
                            self.knowledge_service._fill_log_data(self.auth_context, log_data)
                            
                            # 验证结果
                            assert log_data[0].kb_name == "测试知识库"  # 存在的知识库应该有名称
                            assert log_data[1].kb_name is None  # 不存在的知识库应该为None
                            assert log_data[2].kb_name is None  # 空ID应该为None
                            
                            print("✅ 测试通过：_fill_log_data 方法正确处理了缺失的知识库ID")
                            
                        except KeyError as e:
                            pytest.fail(f"❌ 测试失败：仍然出现 KeyError: {e}")

    def test_fill_log_data_all_existing_kb_ids(self):
        """测试当所有知识库ID都存在时的正常处理"""
        
        # 模拟日志数据，所有知识库ID都存在
        log_data = [
            Mock(
                kb_id="kb-1",
                target_type="session",
                target_id="session-1"
            ),
            Mock(
                kb_id="kb-2",
                target_type="document",
                target_id="doc-1"
            )
        ]

        # 模拟知识库查询结果
        mock_kb1 = Mock()
        mock_kb1.kb_id = "kb-1"
        mock_kb1.name = "知识库1"
        
        mock_kb2 = Mock()
        mock_kb2.kb_id = "kb-2"
        mock_kb2.name = "知识库2"

        # Mock repository 方法
        with patch.object(
            self.knowledge_service.knowledgebase_repository,
            'list_knowledge_bases',
            return_value=[mock_kb1, mock_kb2]
        ):
            with patch.object(
                self.knowledge_service.kb_sessions_repository,
                'list_sessions',
                return_value=[]
            ):
                with patch.object(
                    self.knowledge_service.kb_documents_repository,
                    'list_kb_documents',
                    return_value=[]
                ):
                    with patch('src.domain.services.session_service.session_service.get_sessions_by_ids', return_value=[]):
                        # 调用被测试的方法
                        self.knowledge_service._fill_log_data(self.auth_context, log_data)
                        
                        # 验证结果
                        assert log_data[0].kb_name == "知识库1"
                        assert log_data[1].kb_name == "知识库2"
                        
                        print("✅ 测试通过：_fill_log_data 方法正确处理了所有存在的知识库ID")

    def test_fill_log_data_empty_data(self):
        """测试空数据的处理"""
        
        log_data = []
        
        # Mock repository 方法
        with patch.object(
            self.knowledge_service.knowledgebase_repository,
            'list_knowledge_bases',
            return_value=[]
        ):
            with patch.object(
                self.knowledge_service.kb_sessions_repository,
                'list_sessions',
                return_value=[]
            ):
                with patch.object(
                    self.knowledge_service.kb_documents_repository,
                    'list_kb_documents',
                    return_value=[]
                ):
                    with patch('src.domain.services.session_service.session_service.get_sessions_by_ids', return_value=[]):
                        # 调用被测试的方法
                        try:
                            self.knowledge_service._fill_log_data(self.auth_context, log_data)
                            print("✅ 测试通过：_fill_log_data 方法正确处理了空数据")
                        except Exception as e:
                            pytest.fail(f"❌ 测试失败：处理空数据时出错: {e}")


if __name__ == "__main__":
    # 运行测试
    test_instance = TestKnowledgeServiceFix()
    test_instance.setup_method()
    
    print("开始测试知识库服务 KeyError 修复...")
    
    test_instance.test_fill_log_data_with_missing_kb_id()
    test_instance.test_fill_log_data_all_existing_kb_ids()
    test_instance.test_fill_log_data_empty_data()
    
    print("\n🎉 所有测试完成！")
