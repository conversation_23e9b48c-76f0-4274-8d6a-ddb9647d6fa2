#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MessageProcessor中HTML内容清理功能
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.message_processor import MessageProcessor


class TestMessageProcessorHTMLCleaning(unittest.TestCase):
    """测试MessageProcessor的HTML内容清理功能"""

    def setUp(self):
        """设置测试环境"""
        # 创建MessageProcessor实例
        self.processor = MessageProcessor()

    def test_clean_html_content_with_markdown_tags(self):
        """测试清理带有markdown标记的HTML内容"""
        # 测试数据：带有```html标记的内容
        html_content_with_tags = """```html
<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>This is a test page.</p>
</body>
</html>
```"""

        expected_clean_content = """<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>This is a test page.</p>
</body>
</html>"""

        # 调用清理方法
        result = self.processor._clean_file_content(html_content_with_tags)
        
        # 验证结果
        self.assertEqual(result.strip(), expected_clean_content.strip())

    def test_clean_html_content_case_insensitive(self):
        """测试大小写不敏感的HTML标记清理"""
        html_content_with_tags = """```HTML
<div>Test content</div>
```"""

        expected_clean_content = "<div>Test content</div>"

        result = self.processor._clean_file_content(html_content_with_tags)
        self.assertEqual(result.strip(), expected_clean_content.strip())

    def test_clean_json_content_with_markdown_tags(self):
        """测试清理带有markdown标记的JSON内容"""
        json_content_with_tags = """```json
{
    "name": "test",
    "value": 123
}
```"""

        expected_clean_content = """{
    "name": "test",
    "value": 123
}"""

        result = self.processor._clean_file_content(json_content_with_tags)
        self.assertEqual(result.strip(), expected_clean_content.strip())

    def test_clean_generic_code_block(self):
        """测试清理通用代码块"""
        code_content_with_tags = """```
Some plain text content
without specific language tag
```"""

        expected_clean_content = """Some plain text content
without specific language tag"""

        result = self.processor._clean_file_content(code_content_with_tags)
        self.assertEqual(result.strip(), expected_clean_content.strip())

    def test_no_markdown_tags(self):
        """测试没有markdown标记的内容"""
        plain_content = """<!DOCTYPE html>
<html>
<body>
    <h1>Plain HTML</h1>
</body>
</html>"""

        # 没有markdown标记，应该返回原内容
        result = self.processor._clean_file_content(plain_content)
        self.assertEqual(result, plain_content)

    def test_non_string_content(self):
        """测试非字符串内容"""
        non_string_content = b"binary content"
        
        # 非字符串内容应该原样返回
        result = self.processor._clean_file_content(non_string_content)
        self.assertEqual(result, non_string_content)

    def test_empty_content(self):
        """测试空内容"""
        empty_content = ""
        
        result = self.processor._clean_file_content(empty_content)
        self.assertEqual(result, empty_content)

    def test_malformed_markdown_tags(self):
        """测试格式不正确的markdown标记"""
        malformed_content = """```html
<div>Incomplete markdown block"""

        # 格式不正确的标记应该返回原内容
        result = self.processor._clean_file_content(malformed_content)
        self.assertEqual(result, malformed_content)

    def test_nested_code_blocks(self):
        """测试嵌套的代码块（边界情况）"""
        nested_content = """```html
<pre><code>
```javascript
console.log('nested');
```
</code></pre>
```"""

        # 应该只处理最外层的代码块
        expected_clean_content = """<pre><code>
```javascript
console.log('nested');
```
</code></pre>"""

        result = self.processor._clean_file_content(nested_content)
        self.assertEqual(result.strip(), expected_clean_content.strip())


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)
