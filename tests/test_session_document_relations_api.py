#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会话文档关联API接口
"""

import pytest
import requests
import json
from datetime import datetime
from typing import List, Dict, Any


class TestSessionDocumentRelationsAPI:
    """测试会话文档关联API"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test_token"  # 需要根据实际情况修改
        }
    
    def test_list_session_document_relations(self):
        """测试获取会话文档关联接口"""
        # 测试参数
        kb_id = "test_kb_001"
        session_id = "test_session_001"
        
        # 构造请求URL
        url = f"{self.base_url}/api/knowledge_base/session/document_relations"
        params = {
            "kb_id": kb_id,
            "session_id": session_id
        }
        
        try:
            # 发送请求
            response = requests.get(url, params=params, headers=self.headers)
            
            print(f"请求URL: {response.url}")
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
            # 验证响应
            assert response.status_code == 200, f"期望状态码200，实际得到{response.status_code}"
            
            # 解析响应JSON
            response_data = response.json()
            
            # 验证响应结构
            assert "success" in response_data, "响应中缺少success字段"
            assert "data" in response_data, "响应中缺少data字段"
            assert "request_id" in response_data, "响应中缺少request_id字段"
            
            # 验证数据结构
            if response_data["success"] and response_data["data"]:
                relations = response_data["data"]
                assert isinstance(relations, list), "data字段应该是列表类型"
                
                if len(relations) > 0:
                    # 验证第一个关联对象的结构
                    relation = relations[0]
                    required_fields = ["id", "kb_id", "file_id", "session_id", "gmt_created", "gmt_modified"]
                    
                    for field in required_fields:
                        assert field in relation, f"关联对象中缺少{field}字段"
                    
                    # 验证字段类型
                    assert isinstance(relation["id"], int), "id字段应该是整数类型"
                    assert isinstance(relation["kb_id"], str), "kb_id字段应该是字符串类型"
                    assert isinstance(relation["file_id"], str), "file_id字段应该是字符串类型"
                    assert isinstance(relation["gmt_created"], str), "gmt_created字段应该是字符串类型"
                    assert isinstance(relation["gmt_modified"], str), "gmt_modified字段应该是字符串类型"
                    
                    print(f"找到 {len(relations)} 个文档关联")
                    for i, rel in enumerate(relations):
                        print(f"关联 {i+1}: ID={rel['id']}, KB_ID={rel['kb_id']}, FILE_ID={rel['file_id']}, SESSION_ID={rel.get('session_id')}")
                else:
                    print("该会话下没有找到文档关联")
            
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return False
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return False
        except AssertionError as e:
            print(f"断言失败: {e}")
            return False
        except Exception as e:
            print(f"测试失败: {e}")
            return False
    
    def test_list_session_document_relations_with_invalid_params(self):
        """测试无效参数的情况"""
        test_cases = [
            {"kb_id": "", "session_id": "test_session_001", "desc": "空的kb_id"},
            {"kb_id": "test_kb_001", "session_id": "", "desc": "空的session_id"},
            {"session_id": "test_session_001", "desc": "缺少kb_id参数"},
            {"kb_id": "test_kb_001", "desc": "缺少session_id参数"},
        ]
        
        url = f"{self.base_url}/api/knowledge_base/session/document_relations"
        
        for case in test_cases:
            try:
                params = {k: v for k, v in case.items() if k not in ["desc"]}
                response = requests.get(url, params=params, headers=self.headers)
                
                print(f"\n测试用例: {case['desc']}")
                print(f"请求参数: {params}")
                print(f"响应状态码: {response.status_code}")
                print(f"响应内容: {response.text}")
                
                # 对于无效参数，应该返回400或422状态码
                assert response.status_code in [400, 422], f"期望状态码400或422，实际得到{response.status_code}"
                
            except Exception as e:
                print(f"测试用例 '{case['desc']}' 失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("开始测试会话文档关联API接口")
        print("=" * 60)
        
        # 测试正常情况
        print("\n1. 测试正常获取会话文档关联")
        success1 = self.test_list_session_document_relations()
        
        # 测试异常情况
        print("\n2. 测试无效参数情况")
        self.test_list_session_document_relations_with_invalid_params()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return success1


def main():
    """主函数"""
    tester = TestSessionDocumentRelationsAPI()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
