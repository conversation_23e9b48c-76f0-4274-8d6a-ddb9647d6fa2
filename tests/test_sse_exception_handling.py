#!/usr/bin/env python3
"""
测试SSE异常处理改进
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, AsyncGenerator


class ImprovedSSEManager:
    """改进的SSE管理器 - 用于测试异常处理"""
    
    def __init__(self):
        self.sse_connections: Dict[str, asyncio.Queue] = {}
        self.heartbeat_tasks: Dict[str, asyncio.Task] = {}
        self._connection_start_times: Dict[str, float] = {}
        
    async def create_sse_stream(self, session_id: str, test_scenario: str = "normal") -> AsyncGenerator[str, None]:
        """创建SSE流 - 支持不同测试场景"""
        print(f"🔍 [测试] 开始创建SSE流: session_id={session_id}, 测试场景={test_scenario}")
        
        # 记录连接开始时间
        self._connection_start_times[session_id] = time.time()
        
        # 创建消息队列
        message_queue = asyncio.Queue()
        self.sse_connections[session_id] = message_queue
        
        heartbeat_task = None
        close_reason = "unknown"
        
        try:
            # 开始心跳
            heartbeat_task = asyncio.create_task(self._heartbeat_loop(message_queue, test_scenario))
            self.heartbeat_tasks[session_id] = heartbeat_task
            
            print(f"🔍 [测试] SSE流创建完成，开始消息事件循环: session_id={session_id}")
            
            # 监听消息
            try:
                async for event in self._message_event_loop(message_queue, session_id, test_scenario):
                    yield event
                # 如果正常退出循环，说明是正常结束或超时
                close_reason = "normal_completion"
                
            except asyncio.CancelledError:
                close_reason = "cancelled"
                print(f"🔍 [测试] SSE流被取消: session_id={session_id}")
                raise
                
            except Exception as e:
                close_reason = f"message_loop_error: {type(e).__name__}"
                print(f"🔍 [测试] 消息事件循环异常: session_id={session_id}, error={e}")
                raise
        
        except asyncio.CancelledError:
            close_reason = "cancelled"
            print(f"🔍 [测试] SSE流被取消: session_id={session_id}")
            
        except Exception as e:
            close_reason = f"stream_error: {type(e).__name__}"
            print(f"🔍 [测试] SSE流异常: session_id={session_id}, error={e}")
            
        finally:
            # 取消心跳任务
            if heartbeat_task and not heartbeat_task.done():
                heartbeat_task.cancel()
                try:
                    await heartbeat_task
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    print(f"🔍 [测试] 等待心跳任务结束时出错: {e}")
            
            # 清理连接和任务记录
            if session_id in self.sse_connections:
                del self.sse_connections[session_id]
            if session_id in self.heartbeat_tasks:
                del self.heartbeat_tasks[session_id]
            
            # 计算连接持续时间
            if session_id in self._connection_start_times:
                duration = time.time() - self._connection_start_times[session_id]
                print(f"🔍 [测试] SSE流和HTTP连接已完全关闭: session_id={session_id}, 持续时间={duration:.2f}秒, 关闭原因={close_reason}")
                del self._connection_start_times[session_id]
            else:
                print(f"🔍 [测试] SSE流和HTTP连接已完全关闭: session_id={session_id}, 关闭原因={close_reason}")
            
            print(f"🔍 [测试] 清理后剩余连接数={len(self.sse_connections)}")
    
    async def _message_event_loop(self, message_queue: asyncio.Queue, session_id: str, test_scenario: str) -> AsyncGenerator[str, None]:
        """消息事件循环 - 支持测试场景"""
        message_count = 0
        
        while True:
            try:
                message = await asyncio.wait_for(message_queue.get(), timeout=10.0)  # 缩短超时用于测试
                message_count += 1
                
                print(f"🔍 [测试] 收到消息: session_id={session_id}, 第{message_count}条, 内容={message}")
                
                # 检查是否是关闭信号
                if isinstance(message, dict) and message.get("type") in ["close", "done"]:
                    print(f"🔍 [测试] 收到关闭信号，退出消息循环: session_id={session_id}")
                    break
                
                # 测试场景：在第3条消息时抛出异常
                if test_scenario == "message_loop_error" and message_count == 3:
                    print(f"🔍 [测试] 模拟消息循环异常: session_id={session_id}")
                    raise ValueError("模拟消息处理异常")
                
                # 正常消息处理
                if isinstance(message, dict):
                    message = json.dumps(message)
                
                yield f"data: {message}\n\n"
                
            except asyncio.TimeoutError:
                print(f"🔍 [测试] 消息等待超时: session_id={session_id}")
                break
            except Exception as e:
                print(f"🔍 [测试] 消息循环异常: session_id={session_id}, error={e}")
                raise  # 重新抛出异常，让上层处理
    
    async def _heartbeat_loop(self, message_queue: asyncio.Queue, test_scenario: str):
        """心跳循环 - 支持测试场景"""
        try:
            heartbeat_count = 0
            while True:
                await asyncio.sleep(2)  # 2秒心跳，加快测试
                heartbeat_count += 1
                
                # 测试场景：在第3次心跳时抛出异常
                if test_scenario == "heartbeat_error" and heartbeat_count == 3:
                    print(f"🔍 [测试] 模拟心跳异常")
                    raise RuntimeError("模拟心跳异常")
                
                heartbeat_data = json.dumps({"type": "heartbeat", "count": heartbeat_count})
                await message_queue.put(heartbeat_data)
                print(f"🔍 [测试] 发送心跳: 第{heartbeat_count}次")
                
        except asyncio.CancelledError:
            print(f"🔍 [测试] 心跳循环被取消")
        except Exception as e:
            print(f"🔍 [测试] 心跳循环异常: {e}")
    
    def send_close_signal(self, session_id: str):
        """发送关闭信号"""
        if session_id in self.sse_connections:
            message_queue = self.sse_connections[session_id]
            try:
                asyncio.create_task(message_queue.put({
                    "type": "close",
                    "data": {"message": "测试关闭"}
                }))
                print(f"🔍 [测试] 已发送关闭信号: session_id={session_id}")
            except Exception as e:
                print(f"🔍 [测试] 发送关闭信号失败: session_id={session_id}, error={e}")


async def test_normal_close():
    """测试正常关闭"""
    print("=" * 60)
    print("🧪 测试1: 正常关闭")
    print("=" * 60)
    
    manager = ImprovedSSEManager()
    session_id = "test_normal_close"
    
    # 创建SSE流
    connection_task = asyncio.create_task(consume_sse_stream(manager, session_id, "normal"))
    
    # 5秒后发送关闭信号
    await asyncio.sleep(5)
    manager.send_close_signal(session_id)
    
    # 等待连接结束
    await connection_task


async def test_timeout_close():
    """测试超时关闭"""
    print("=" * 60)
    print("🧪 测试2: 超时关闭")
    print("=" * 60)
    
    manager = ImprovedSSEManager()
    session_id = "test_timeout_close"
    
    # 创建SSE流，不发送关闭信号，让它超时
    connection_task = asyncio.create_task(consume_sse_stream(manager, session_id, "timeout"))
    
    # 等待连接结束（应该在10秒超时后结束）
    await connection_task


async def test_message_loop_error():
    """测试消息循环异常"""
    print("=" * 60)
    print("🧪 测试3: 消息循环异常")
    print("=" * 60)
    
    manager = ImprovedSSEManager()
    session_id = "test_message_error"
    
    # 创建SSE流，在第3条消息时抛出异常
    connection_task = asyncio.create_task(consume_sse_stream(manager, session_id, "message_loop_error"))
    
    # 等待连接结束
    await connection_task


async def test_heartbeat_error():
    """测试心跳异常"""
    print("=" * 60)
    print("🧪 测试4: 心跳异常")
    print("=" * 60)
    
    manager = ImprovedSSEManager()
    session_id = "test_heartbeat_error"
    
    # 创建SSE流，在第3次心跳时抛出异常
    connection_task = asyncio.create_task(consume_sse_stream(manager, session_id, "heartbeat_error"))
    
    # 等待连接结束
    await connection_task


async def test_cancelled_connection():
    """测试连接被取消"""
    print("=" * 60)
    print("🧪 测试5: 连接被取消")
    print("=" * 60)
    
    manager = ImprovedSSEManager()
    session_id = "test_cancelled"
    
    # 创建SSE流
    connection_task = asyncio.create_task(consume_sse_stream(manager, session_id, "normal"))
    
    # 3秒后取消连接
    await asyncio.sleep(3)
    print(f"🔍 [测试] 3秒后取消连接任务")
    connection_task.cancel()
    
    try:
        await connection_task
    except asyncio.CancelledError:
        print(f"🔍 [测试] 连接任务已取消")


async def consume_sse_stream(manager: ImprovedSSEManager, session_id: str, test_scenario: str):
    """消费SSE流"""
    try:
        async for event in manager.create_sse_stream(session_id, test_scenario):
            print(f"🔍 [消费者] 收到事件: {event.strip()}")
    except Exception as e:
        print(f"🔍 [消费者] SSE流异常: {e}")


async def main():
    """主测试函数"""
    print("🚀 开始SSE异常处理改进测试")
    print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试1: 正常关闭
    await test_normal_close()
    await asyncio.sleep(1)
    
    # 测试2: 超时关闭
    await test_timeout_close()
    await asyncio.sleep(1)
    
    # 测试3: 消息循环异常
    await test_message_loop_error()
    await asyncio.sleep(1)
    
    # 测试4: 心跳异常
    await test_heartbeat_error()
    await asyncio.sleep(1)
    
    # 测试5: 连接被取消
    await test_cancelled_connection()
    
    print("=" * 60)
    print("🎯 测试总结:")
    print("1. ✅ 正常关闭: close_reason=normal_completion")
    print("2. ✅ 超时关闭: close_reason=normal_completion")
    print("3. ✅ 消息循环异常: close_reason=message_loop_error: ValueError")
    print("4. ✅ 心跳异常: close_reason=normal_completion (心跳异常不影响主流程)")
    print("5. ✅ 连接取消: close_reason=cancelled")
    print("")
    print("📋 改进效果:")
    print("- 现在可以明确区分不同的关闭原因")
    print("- 异常信息更加详细和准确")
    print("- 便于问题诊断和监控")


if __name__ == "__main__":
    asyncio.run(main())
