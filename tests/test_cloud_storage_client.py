# -*- coding: utf-8 -*-
"""
CloudStorageClient 测试脚本
"""
import sys
import os
import pytest
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.popclients.cloud_storage_client import (
    CloudStorageClient,
    CloudStorageClientError,
    get_cloud_storage_client,
    reset_cloud_storage_client
)


class TestCloudStorageClient:
    """CloudStorageClient 测试类"""

    def setup_method(self):
        """每个测试方法前的设置"""
        reset_cloud_storage_client()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    @patch('src.popclients.cloud_storage_client.client.Client')
    def test_client_initialization(self, mock_client, mock_get_credential, mock_env_manager):
        """测试客户端初始化"""
        # 模拟配置
        mock_config = Mock()
        mock_config.ram_role_arn = "acs:ram::123456789:role/test-role"
        mock_config.region_id = "cn-hangzhou"
        mock_env_manager.get_config.return_value = mock_config
        mock_env_manager.current_env.value = "test"
        
        # 模拟凭证
        mock_credential = Mock()
        mock_get_credential.return_value = mock_credential
        
        # 创建客户端
        client = CloudStorageClient()
        
        # 验证初始化
        assert client.ram_role_arn == "acs:ram::123456789:role/test-role"
        assert client.region_id == "cn-hangzhou"
        assert client.endpoint == "eds-storage-inner.cn-hangzhou.aliyuncs.com"
        assert client.connect_timeout == 5000
        assert client.read_timeout == 10000
        
        # 验证凭证获取被调用
        mock_get_credential.assert_called_once_with("acs:ram::123456789:role/test-role")
        
        # 验证客户端被创建
        mock_client.assert_called_once()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    @patch('src.popclients.cloud_storage_client.client.Client')
    def test_pre_upload_file(self, mock_client, mock_get_credential, mock_env_manager):
        """测试预上传文件方法"""
        # 设置模拟
        self._setup_mocks(mock_env_manager, mock_get_credential)
        
        # 模拟客户端响应
        mock_response = Mock()
        mock_client_instance = Mock()
        mock_client_instance.pre_upload_file.return_value = mock_response
        mock_client.return_value = mock_client_instance
        
        # 创建客户端并调用方法
        client = CloudStorageClient()
        result = client.pre_upload_file(
            file_path="/test/file.txt",
            product_type="test",
            user_ali_uid="123456",
            wy_drive_owner_id="owner123"
        )
        
        # 验证结果
        assert result == mock_response
        mock_client_instance.pre_upload_file.assert_called_once()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    @patch('src.popclients.cloud_storage_client.client.Client')
    def test_complete_upload_file(self, mock_client, mock_get_credential, mock_env_manager):
        """测试完成文件上传方法"""
        # 设置模拟
        self._setup_mocks(mock_env_manager, mock_get_credential)
        
        # 模拟客户端响应
        mock_response = Mock()
        mock_client_instance = Mock()
        mock_client_instance.complete_upload_file.return_value = mock_response
        mock_client.return_value = mock_client_instance
        
        # 创建客户端并调用方法
        client = CloudStorageClient()
        result = client.complete_upload_file(
            file_path="/test/file.txt",
            product_type="test",
            user_ali_uid="123456",
            wy_drive_owner_id="owner123"
        )
        
        # 验证结果
        assert result == mock_response
        mock_client_instance.complete_upload_file.assert_called_once()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    @patch('src.popclients.cloud_storage_client.client.Client')
    def test_get_download_url_by_path(self, mock_client, mock_get_credential, mock_env_manager):
        """测试通过路径获取下载URL方法"""
        # 设置模拟
        self._setup_mocks(mock_env_manager, mock_get_credential)
        
        # 模拟客户端响应
        mock_response = Mock()
        mock_client_instance = Mock()
        mock_client_instance.get_download_url_by_path.return_value = mock_response
        mock_client.return_value = mock_client_instance
        
        # 创建客户端并调用方法
        client = CloudStorageClient()
        result = client.get_download_url_by_path(
            file_path="/test/file.txt",
            product_type="test",
            user_ali_uid="123456",
            wy_drive_owner_id="owner123"
        )
        
        # 验证结果
        assert result == mock_response
        mock_client_instance.get_download_url_by_path.assert_called_once()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    @patch('src.popclients.cloud_storage_client.client.Client')
    def test_describe_wy_drive_file_flat(self, mock_client, mock_get_credential, mock_env_manager):
        """测试查询无影云盘文件方法"""
        # 设置模拟
        self._setup_mocks(mock_env_manager, mock_get_credential)
        
        # 模拟客户端响应
        mock_response = Mock()
        mock_client_instance = Mock()
        mock_client_instance.describe_wy_drive_file_flat.return_value = mock_response
        mock_client.return_value = mock_client_instance
        
        # 创建客户端并调用方法
        client = CloudStorageClient()
        result = client.describe_wy_drive_file_flat(
            user_ali_uid="123456",
            wy_drive_owner_id="owner123"
        )
        
        # 验证结果
        assert result == mock_response
        mock_client_instance.describe_wy_drive_file_flat.assert_called_once()

    @patch('src.popclients.cloud_storage_client.env_manager')
    @patch('src.popclients.cloud_storage_client.get_akless_credential')
    def test_error_handling(self, mock_get_credential, mock_env_manager):
        """测试错误处理"""
        # 模拟配置错误
        mock_env_manager.get_config.side_effect = Exception("配置读取失败")
        
        # 验证异常被正确抛出
        with pytest.raises(CloudStorageClientError) as exc_info:
            CloudStorageClient()
        
        assert "无法从配置中读取RAM角色ARN" in str(exc_info.value)

    def test_singleton_pattern(self):
        """测试单例模式"""
        with patch('src.popclients.cloud_storage_client.CloudStorageClient') as mock_client_class:
            mock_instance = Mock()
            mock_client_class.return_value = mock_instance
            
            # 第一次调用
            client1 = get_cloud_storage_client()
            # 第二次调用
            client2 = get_cloud_storage_client()
            
            # 验证返回同一个实例
            assert client1 == client2
            # 验证只创建了一次
            mock_client_class.assert_called_once()

    def test_client_info(self):
        """测试获取客户端信息"""
        with patch('src.popclients.cloud_storage_client.env_manager') as mock_env_manager, \
             patch('src.popclients.cloud_storage_client.get_akless_credential') as mock_get_credential, \
             patch('src.popclients.cloud_storage_client.client.Client'):
            
            # 设置模拟
            self._setup_mocks(mock_env_manager, mock_get_credential)
            
            # 创建客户端
            client = CloudStorageClient()
            
            # 获取客户端信息
            info = client.get_client_info()
            
            # 验证信息
            expected_info = {
                "ram_role_arn": "acs:ram::123456789:role/test-role",
                "region_id": "cn-hangzhou",
                "endpoint": "ecd-inner-share.cn-hangzhou.aliyuncs.com",
                "connect_timeout": 5000,
                "read_timeout": 10000
            }
            assert info == expected_info

    def test_string_representation(self):
        """测试字符串表示"""
        with patch('src.popclients.cloud_storage_client.env_manager') as mock_env_manager, \
             patch('src.popclients.cloud_storage_client.get_akless_credential') as mock_get_credential, \
             patch('src.popclients.cloud_storage_client.client.Client'):
            
            # 设置模拟
            self._setup_mocks(mock_env_manager, mock_get_credential)
            
            # 创建客户端
            client = CloudStorageClient()
            
            # 验证字符串表示
            expected_str = "CloudStorageClient(endpoint=eds-storage-inner.cn-hangzhou.aliyuncs.com, ram_role_arn=acs:ram::123456789:role/test-role)"
            assert str(client) == expected_str
            assert repr(client) == expected_str

    def _setup_mocks(self, mock_env_manager, mock_get_credential):
        """设置通用模拟"""
        # 模拟配置
        mock_config = Mock()
        mock_config.ram_role_arn = "acs:ram::123456789:role/test-role"
        mock_config.region_id = "cn-hangzhou"
        mock_env_manager.get_config.return_value = mock_config
        mock_env_manager.current_env.value = "test"
        
        # 模拟凭证
        mock_credential = Mock()
        mock_get_credential.return_value = mock_credential


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
