#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的消息流程测试
直接调用服务层方法，绕过HTTP认证，专注测试消息处理逻辑
"""

import asyncio
import json
import time
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.session_service import session_service
from src.domain.services.auth_service import AuthContext


class SimpleMessageFlowTester:
    """简化的消息流程测试器"""
    
    def __init__(self):
        self.session_id: Optional[str] = None
        
    async def test_send_message_direct(self, agent_id: str = "alpha", message: str = "测试消息1234124") -> str:
        """直接调用服务层发送消息"""
        print(f"\n=== 直接测试发送消息 ===")
        print(f"Agent ID: {agent_id}")
        print(f"消息内容: {message}")
        
        # 创建测试用的认证上下文
        auth_context = AuthContext(
            ali_uid=*********,
            wy_id="test_user",
            end_user_id="test_end_user",
            account_type="TEST"
        )
        
        try:
            # 直接调用服务层方法
            session_id, round_id = await session_service.send_message(
                session_id=None,  # 创建新会话
                prompt=message,
                agent_id=agent_id,
                desktop_id=None,
                auth_code=None,
                resources=[],
                context=auth_context
            )
            
            print(f"✅ 消息发送成功")
            print(f"Session ID: {session_id}")
            print(f"Round ID: {round_id}")
            
            self.session_id = session_id
            return session_id
            
        except Exception as e:
            print(f"❌ 消息发送失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_stream_direct(self, session_id: str, duration: int = 30) -> bool:
        """直接测试SSE流"""
        print(f"\n=== 直接测试SSE流 ===")
        print(f"Session ID: {session_id}")
        print(f"监听时长: {duration}秒")
        
        try:
            message_count = 0
            start_time = time.time()
            
            print("✅ 开始监听SSE流...")
            
            # 直接调用服务层的SSE流方法
            async for event_data in session_service.create_sse_stream(
                session_id=session_id,
                last_message_id=None
            ):
                current_time = time.time()
                elapsed = current_time - start_time
                
                print(f"[{elapsed:.1f}s] 收到SSE事件: {event_data[:200]}...")
                
                # 尝试解析事件数据
                try:
                    if event_data.startswith("data: "):
                        data_str = event_data[6:].strip()
                        if data_str and data_str != "":
                            data = json.loads(data_str)
                            message_type = data.get("type", "unknown")
                            
                            if message_type == "heartbeat":
                                print(f"  💓 心跳消息")
                            elif message_type in ["TEXT_MESSAGE_CONTENT", "TEXT_MESSAGE_DELTA_CONTENT"]:
                                message_count += 1
                                content = data.get("content", "")
                                print(f"  📝 消息内容 #{message_count}: {content[:100]}...")
                            elif message_type in ["RUN_FINISHED", "RUN_ERROR"]:
                                print(f"  🏁 会话完成: {message_type}")
                                print("✅ 检测到会话完成，连接即将关闭")
                                return True
                            elif message_type == "done":
                                print(f"  ✅ Done事件")
                                print("✅ 检测到done事件，会话完成")
                                return True
                            else:
                                print(f"  📨 其他消息: {message_type}")
                                
                except json.JSONDecodeError:
                    print(f"  ⚠️ 无法解析的数据: {event_data.strip()}")
                
                # 检查是否超时
                if elapsed > duration:
                    print(f"⏰ 监听时间达到 {duration}秒，结束测试")
                    break
            
            print(f"📊 测试结果: 共收到 {message_count} 条消息")
            return message_count > 0
            
        except Exception as e:
            print(f"❌ SSE流测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def test_message_processor_direct(self):
        """直接测试消息处理器"""
        print(f"\n=== 直接测试消息处理器 ===")
        
        try:
            from src.domain.services.message_processor import MessageProcessor
            from memory.events import Event, EventType
            
            # 创建消息处理器
            processor = MessageProcessor()
            
            # 创建测试事件
            class TestEvent:
                def __init__(self):
                    self.event_id = "test_event_123"
                    self.type = EventType.TEXT_MESSAGE_CONTENT
                    self.session_id = "test_session_456"
                    self.run_id = "test_run_789"
                    self.content = "这是一个测试消息"
                    self.role = "assistant"
                    self.message_id = "test_message_123"
                    self.timestamp = int(time.time() * 1000)
                    
                def __str__(self):
                    return f"TestEvent(id={self.event_id}, type={self.type})"
            
            event = TestEvent()
            
            print(f"创建测试事件: {event}")
            
            # 直接调用消息处理器
            await processor.handle_new_message(event)
            
            print("✅ 消息处理器测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 消息处理器测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def run_full_test(self, agent_id: str = "alpha", message: str = "测试消息1234124"):
        """运行完整的简化测试"""
        print("🚀 开始简化的消息流程测试")
        print("=" * 50)
        
        # 首先测试消息处理器
        print("步骤0: 测试消息处理器")
        processor_success = await self.test_message_processor_direct()
        if not processor_success:
            print("❌ 消息处理器测试失败")
        
        # 步骤1: 发送消息
        print("\n步骤1: 发送消息")
        session_id = await self.test_send_message_direct(agent_id, message)
        if not session_id:
            print("❌ 发送消息失败，测试终止")
            return False
        
        # 等待一下，让消息处理有时间启动
        print("\n⏳ 等待5秒让消息处理启动...")
        await asyncio.sleep(5)
        
        # 步骤2: 测试SSE流
        print("\n步骤2: 测试SSE流")
        success = await self.test_stream_direct(session_id, duration=60)
        
        if success:
            print("\n🎉 简化的消息流程测试成功！")
            print("✅ 消息发送成功")
            print("✅ SSE流建立成功")
            print("✅ 消息接收成功")
        else:
            print("\n❌ 消息流程测试失败")
            print("可能的原因:")
            print("1. 消息处理逻辑有问题")
            print("2. SSE流生成失败")
            print("3. 消息没有正确推送")
        
        return success


async def test_service_initialization():
    """测试服务初始化"""
    print("🔍 检查服务初始化状态...")
    
    try:
        # 检查session_service是否已初始化
        if hasattr(session_service, 'message_processor') and session_service.message_processor:
            print("✅ SessionService 已初始化")
            print(f"✅ MessageProcessor 已设置: {type(session_service.message_processor)}")
            
            if hasattr(session_service, 'sse_stream_manager') and session_service.sse_stream_manager:
                print("✅ SSE Stream Manager 已设置")
            else:
                print("⚠️ SSE Stream Manager 未设置")
                
            return True
        else:
            print("❌ SessionService 未正确初始化")
            return False
            
    except Exception as e:
        print(f"❌ 服务检查失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 Alpha Service 简化消息流程测试")
    print("=" * 60)
    
    # 检查服务状态
    if not await test_service_initialization():
        print("\n❌ 服务未正确初始化，请检查配置")
        return
    
    # 创建测试器
    tester = SimpleMessageFlowTester()
    
    # 运行测试
    success = await tester.run_full_test(
        agent_id="alpha",
        message="你好，这是一个测试消息，请回复确认收到。当前时间：" + str(time.time())
    )
    
    if success:
        print("\n🎊 所有测试通过！消息流程工作正常")
    else:
        print("\n💥 测试失败，请检查日志和配置")


if __name__ == "__main__":
    asyncio.run(main())
