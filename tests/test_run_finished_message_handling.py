#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 RUN_FINISHED 消息处理修复
验证即使没有SSE连接，RUN_FINISHED事件也能正确处理
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.domain.services.message_processor import MessageProcessor
from memory.events import EventType


class MockEvent:
    """模拟Event对象"""
    def __init__(self, event_type, session_id="test_session", run_id="test_run", event_id="test_event"):
        self.type = event_type
        self.session_id = session_id
        self.run_id = run_id
        self.event_id = event_id
        self.content = "test content"
        
    def model_dump(self):
        return {
            'type': self.type.value if hasattr(self.type, 'value') else str(self.type),
            'session_id': self.session_id,
            'run_id': self.run_id,
            'event_id': self.event_id,
            'content': self.content
        }


class MockSession:
    """模拟Session对象"""
    def __init__(self, session_id="test_session"):
        self.session_id = session_id
        self.ali_uid = "test_uid"
        self.wy_id = "test_wy_id"
        self.agent_id = "test_agent"
        self.title = "Test Session"
        self.status = "ACTIVE"
        self.gmt_create = None
        self.gmt_modified = None
        self.metadata = {}
        
    def finish_processing(self):
        """模拟完成处理"""
        self.status = "CLOSED"


class MockSSEManager:
    """模拟SSE管理器"""
    def __init__(self, has_connection=False):
        self.sse_connections = {}
        if has_connection:
            self.sse_connections["test_session"] = Mock()
            
    async def push_to_sse(self, session_id, round_id, data, event_type="message"):
        """模拟推送到SSE"""
        pass
        
    def close_session_connection(self, session_id):
        """模拟关闭连接"""
        if session_id in self.sse_connections:
            del self.sse_connections[session_id]


class TestRunFinishedMessageHandling(unittest.TestCase):
    """测试RUN_FINISHED消息处理修复"""

    def setUp(self):
        """设置测试环境"""
        self.processor = MessageProcessor()
        
    async def test_run_finished_without_sse_connection(self):
        """测试没有SSE连接时的RUN_FINISHED事件处理"""
        print("\n=== 测试没有SSE连接时的RUN_FINISHED事件处理 ===")
        
        # 创建没有连接的SSE管理器
        sse_manager = MockSSEManager(has_connection=False)
        self.processor.set_sse_manager(sse_manager)
        
        # 模拟RUN_FINISHED事件
        event = MockEvent(EventType.RUN_FINISHED)
        
        # 模拟Session加载
        mock_session = MockSession()
        
        with patch.object(self.processor, '_load_session_from_db', return_value=mock_session) as mock_load:
            with patch.object(sse_manager, 'push_to_sse', new_callable=AsyncMock) as mock_push:
                with patch.object(sse_manager, 'close_session_connection') as mock_close:
                    
                    # 执行消息处理
                    await self.processor._handle_new_message_internal("test_session", "test_run", event)
                    
                    # 验证Session被加载
                    mock_load.assert_called_once_with("test_session")
                    
                    # 验证Session状态被更新为CLOSED
                    self.assertEqual(mock_session.status, "CLOSED")
                    
                    # 验证没有调用SSE推送（因为没有连接）
                    mock_push.assert_not_called()
                    
                    # 验证没有调用关闭连接（因为没有连接）
                    mock_close.assert_not_called()
                    
                    print("✅ 没有SSE连接时，RUN_FINISHED事件仍能正确处理Session状态")

    async def test_run_finished_with_sse_connection(self):
        """测试有SSE连接时的RUN_FINISHED事件处理"""
        print("\n=== 测试有SSE连接时的RUN_FINISHED事件处理 ===")
        
        # 创建有连接的SSE管理器
        sse_manager = MockSSEManager(has_connection=True)
        self.processor.set_sse_manager(sse_manager)
        
        # 模拟RUN_FINISHED事件
        event = MockEvent(EventType.RUN_FINISHED)
        
        # 模拟Session加载
        mock_session = MockSession()
        
        with patch.object(self.processor, '_load_session_from_db', return_value=mock_session) as mock_load:
            with patch.object(sse_manager, 'push_to_sse', new_callable=AsyncMock) as mock_push:
                with patch.object(sse_manager, 'close_session_connection') as mock_close:
                    with patch('asyncio.sleep', new_callable=AsyncMock):
                        
                        # 执行消息处理
                        await self.processor._handle_new_message_internal("test_session", "test_run", event)
                        
                        # 验证Session被加载
                        mock_load.assert_called_once_with("test_session")
                        
                        # 验证Session状态被更新为CLOSED
                        self.assertEqual(mock_session.status, "CLOSED")
                        
                        # 验证调用了SSE推送（因为有连接）
                        self.assertEqual(mock_push.call_count, 2)  # 一次普通消息，一次done事件
                        
                        # 验证调用了关闭连接（因为有连接）
                        mock_close.assert_called_once_with("test_session")
                        
                        print("✅ 有SSE连接时，RUN_FINISHED事件正确处理并发送done事件")

    async def test_normal_message_without_sse_connection(self):
        """测试没有SSE连接时的普通消息处理"""
        print("\n=== 测试没有SSE连接时的普通消息处理 ===")
        
        # 创建没有连接的SSE管理器
        sse_manager = MockSSEManager(has_connection=False)
        self.processor.set_sse_manager(sse_manager)
        
        # 模拟普通消息事件
        event = MockEvent(EventType.TEXT_MESSAGE_CONTENT)
        
        with patch.object(self.processor, '_load_session_from_db') as mock_load:
            
            # 执行消息处理
            await self.processor._handle_new_message_internal("test_session", "test_run", event)
            
            # 验证Session没有被加载（因为消息处理被跳过）
            mock_load.assert_not_called()
            
            print("✅ 没有SSE连接时，普通消息处理被正确跳过")

    async def test_run_error_without_sse_connection(self):
        """测试没有SSE连接时的RUN_ERROR事件处理"""
        print("\n=== 测试没有SSE连接时的RUN_ERROR事件处理 ===")
        
        # 创建没有连接的SSE管理器
        sse_manager = MockSSEManager(has_connection=False)
        self.processor.set_sse_manager(sse_manager)
        
        # 模拟RUN_ERROR事件
        event = MockEvent(EventType.RUN_ERROR)
        
        # 模拟Session加载
        mock_session = MockSession()
        
        with patch.object(self.processor, '_load_session_from_db', return_value=mock_session) as mock_load:
            
            # 执行消息处理
            await self.processor._handle_new_message_internal("test_session", "test_run", event)
            
            # 验证Session被加载
            mock_load.assert_called_once_with("test_session")
            
            # 验证Session状态被更新为CLOSED
            self.assertEqual(mock_session.status, "CLOSED")
            
            print("✅ 没有SSE连接时，RUN_ERROR事件仍能正确处理Session状态")

    def test_sync_wrapper(self):
        """同步测试包装器"""
        print("开始测试RUN_FINISHED消息处理修复...")
        
        # 运行所有异步测试
        asyncio.run(self.test_run_finished_without_sse_connection())
        asyncio.run(self.test_run_finished_with_sse_connection())
        asyncio.run(self.test_normal_message_without_sse_connection())
        asyncio.run(self.test_run_error_without_sse_connection())
        
        print("\n🎉 所有测试通过！RUN_FINISHED消息处理修复验证成功")


if __name__ == '__main__':
    # 运行测试
    test = TestRunFinishedMessageHandling()
    test.setUp()
    test.test_sync_wrapper()
