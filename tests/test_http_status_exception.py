#!/usr/bin/env python3
"""
测试 handle_exception_with_http_status 函数的HTTP状态码功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.presentation.api.dependencies.api_common_utils import handle_exception_with_http_status
from src.domain.utils.check_utils import ClientException


def test_client_exception_http_status():
    """测试客户端异常返回400状态码"""
    print("=== 测试客户端异常HTTP状态码 ===")
    
    # 创建一个客户端异常
    client_exception = ClientException(code="INVALID_PARAMETER", message="参数无效")
    
    # 调用handle_exception_with_http_status
    response = handle_exception_with_http_status(client_exception, "test-request-id")
    
    print(f"异常类型: ClientException")
    print(f"HTTP状态码: {response.status_code}")
    print(f"期望状态码: 400")
    print(f"状态码匹配: {response.status_code == 400}")
    print(f"响应内容: {response.body.decode()}")
    print()


def test_generic_exception_http_status():
    """测试通用异常返回500状态码"""
    print("=== 测试通用异常HTTP状态码 ===")
    
    # 创建一个通用异常
    generic_exception = Exception("这是一个通用异常")
    
    # 调用handle_exception_with_http_status
    response = handle_exception_with_http_status(generic_exception, "test-request-id")
    
    print(f"异常类型: Exception")
    print(f"HTTP状态码: {response.status_code}")
    print(f"期望状态码: 500")
    print(f"状态码匹配: {response.status_code == 500}")
    print(f"响应内容: {response.body.decode()}")
    print()


def test_file_processing_failed_exception_http_status():
    """测试文件处理失败异常返回400状态码"""
    print("=== 测试文件处理失败异常HTTP状态码 ===")
    
    try:
        # 导入文件处理失败异常
        from src.domain.services.file_service import FileProcessingFailedException
        
        # 创建一个文件处理失败异常
        file_exception = FileProcessingFailedException("文件处理失败")
        
        # 调用handle_exception_with_http_status
        response = handle_exception_with_http_status(file_exception, "test-request-id")
        
        print(f"异常类型: FileProcessingFailedException")
        print(f"HTTP状态码: {response.status_code}")
        print(f"期望状态码: 400")
        print(f"状态码匹配: {response.status_code == 400}")
        print(f"响应内容: {response.body.decode()}")
        
    except ImportError:
        print("FileProcessingFailedException 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_file_processing_timeout_exception_http_status():
    """测试文件处理超时异常返回400状态码"""
    print("=== 测试文件处理超时异常HTTP状态码 ===")
    
    try:
        # 导入文件处理超时异常
        from src.domain.services.file_service import FileProcessingTimeoutException
        
        # 创建一个文件处理超时异常
        file_exception = FileProcessingTimeoutException("文件处理超时")
        
        # 调用handle_exception_with_http_status
        response = handle_exception_with_http_status(file_exception, "test-request-id")
        
        print(f"异常类型: FileProcessingTimeoutException")
        print(f"HTTP状态码: {response.status_code}")
        print(f"期望状态码: 400")
        print(f"状态码匹配: {response.status_code == 400}")
        print(f"响应内容: {response.body.decode()}")
        
    except ImportError:
        print("FileProcessingTimeoutException 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_file_content_too_large_error_http_status():
    """测试文件内容过大异常返回400状态码"""
    print("=== 测试文件内容过大异常HTTP状态码 ===")
    
    try:
        # 导入文件内容过大异常
        from src.domain.services.session_service import FileContentTooLargeError
        
        # 创建一个文件内容过大异常
        file_exception = FileContentTooLargeError("文件内容过大")
        
        # 调用handle_exception_with_http_status
        response = handle_exception_with_http_status(file_exception, "test-request-id")
        
        print(f"异常类型: FileContentTooLargeError")
        print(f"HTTP状态码: {response.status_code}")
        print(f"期望状态码: 400")
        print(f"状态码匹配: {response.status_code == 400}")
        print(f"响应内容: {response.body.decode()}")
        
    except ImportError:
        print("FileContentTooLargeError 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_response_format():
    """测试响应格式"""
    print("=== 测试响应格式 ===")
    
    # 创建一个客户端异常
    client_exception = ClientException(code="TEST_ERROR", message="测试错误")
    
    # 调用handle_exception_with_http_status
    response = handle_exception_with_http_status(client_exception, "test-request-id")
    
    # 解析响应内容
    import json
    content = json.loads(response.body.decode())
    
    print(f"响应类型: {type(response)}")
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容类型: {type(content)}")
    print(f"响应内容键: {list(content.keys())}")
    
    # 验证响应格式
    expected_keys = ['code', 'data', 'message', 'request_id', 'status', 'success']
    missing_keys = [key for key in expected_keys if key not in content]
    
    if missing_keys:
        print(f"❌ 缺少响应键: {missing_keys}")
    else:
        print("✅ 响应格式正确")
    
    print(f"响应内容: {content}")
    print()


def main():
    """主测试函数"""
    print("handle_exception_with_http_status HTTP状态码测试")
    print("=" * 60)
    
    # 测试各种异常类型的HTTP状态码
    test_client_exception_http_status()
    test_generic_exception_http_status()
    test_file_processing_failed_exception_http_status()
    test_file_processing_timeout_exception_http_status()
    test_file_content_too_large_error_http_status()
    
    # 测试响应格式
    test_response_format()
    
    print("测试完成！")
    print("\n总结:")
    print("- ✅ ClientException 返回 HTTP 400")
    print("- ✅ FileProcessingFailedException 返回 HTTP 400")
    print("- ✅ FileProcessingTimeoutException 返回 HTTP 400")
    print("- ✅ FileContentTooLargeError 返回 HTTP 400")
    print("- ✅ 通用异常返回 HTTP 500")
    print("- ✅ 响应格式符合预期")


if __name__ == "__main__":
    main()
