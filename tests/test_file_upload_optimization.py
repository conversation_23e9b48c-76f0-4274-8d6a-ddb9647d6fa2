#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传优化功能测试脚本
测试优化后的 confirm-upload 接口和新的 rag-status 接口
"""

import asyncio
import json
import time
import requests
from typing import Dict, Any, Optional
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class FileUploadOptimizationTester:
    """文件上传优化功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_headers = {}
        
    def setup_auth(self, user_key: str = "test_user", ali_uid: int = 123456, wy_id: str = "test_wy_id"):
        """设置认证信息"""
        self.auth_headers = {
            "X-User-Key": user_key,
            "X-Ali-Uid": str(ali_uid),
            "X-Wy-Id": wy_id,
            "Content-Type": "application/json"
        }
        self.session.headers.update(self.auth_headers)
        print(f"✓ 设置认证信息: user_key={user_key}, ali_uid={ali_uid}, wy_id={wy_id}")
    
    def create_test_file(self, filename: str = "test_document.pdf", size: int = 1024) -> bytes:
        """创建测试文件内容"""
        content = b"PDF test content " * (size // 17 + 1)
        return content[:size]
    
    def test_get_upload_url(self, filename: str = "test_document.pdf", file_size: int = 1024) -> Optional[Dict[str, Any]]:
        """测试获取预签名上传URL"""
        print(f"\n=== 测试获取预签名上传URL ===")
        
        url = f"{self.base_url}/api/files/get-upload-url"
        payload = {
            "file_info": {
                "FileName": filename,
                "FileSize": file_size,
                "FileType": "pdf"
            },
            "file_type": "sessionFile"
        }
        
        try:
            response = self.session.post(url, json=payload)
            print(f"请求URL: {url}")
            print(f"请求载荷: {json.dumps(payload, indent=2)}")
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 获取预签名URL成功")
                print(f"  文件ID: {result['data']['file_id']}")
                print(f"  会话ID: {result['data']['session_id']}")
                return result['data']
            else:
                print(f"✗ 获取预签名URL失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"✗ 请求异常: {e}")
            return None
    
    def simulate_file_upload(self, upload_url: str, file_content: bytes) -> Optional[str]:
        """模拟文件上传到OSS"""
        print(f"\n=== 模拟文件上传到OSS ===")
        
        try:
            # 这里只是模拟，实际应该上传到OSS
            print(f"模拟上传文件到: {upload_url}")
            print(f"文件大小: {len(file_content)} 字节")
            
            # 模拟上传成功，返回ETag
            etag = "mock-etag-12345"
            print(f"✓ 模拟上传成功，ETag: {etag}")
            return etag
            
        except Exception as e:
            print(f"✗ 模拟上传异常: {e}")
            return None
    
    def test_confirm_upload(self, file_id: str, etag: str = "mock-etag-12345") -> bool:
        """测试确认上传接口（优化后）"""
        print(f"\n=== 测试确认上传接口（优化后）===")
        
        url = f"{self.base_url}/api/files/confirm-upload"
        payload = {
            "file_id": file_id,
            "etag": etag
        }
        
        try:
            start_time = time.time()
            response = self.session.post(url, json=payload)
            end_time = time.time()
            
            print(f"请求URL: {url}")
            print(f"请求载荷: {json.dumps(payload, indent=2)}")
            print(f"响应状态码: {response.status_code}")
            print(f"响应时间: {end_time - start_time:.2f} 秒")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✓ 确认上传成功")
                print(f"  状态: {result['data']['status']}")
                print(f"  消息: {result['data']['message']}")
                
                # 验证是否立即返回（不等待RAG解析）
                if end_time - start_time < 5:  # 应该在5秒内返回
                    print(f"✓ 接口优化成功：响应时间 {end_time - start_time:.2f} 秒 < 5秒")
                else:
                    print(f"⚠ 接口可能未优化：响应时间 {end_time - start_time:.2f} 秒 >= 5秒")
                
                return True
            else:
                print(f"✗ 确认上传失败: {response.text}")
                return False
                
        except Exception as e:
            print(f"✗ 请求异常: {e}")
            return False
    
    def test_rag_status(self, file_id: str, max_polls: int = 10, poll_interval: int = 2) -> bool:
        """测试RAG状态查询接口"""
        print(f"\n=== 测试RAG状态查询接口 ===")
        
        url = f"{self.base_url}/api/files/rag-status"
        payload = {
            "file_id": file_id
        }
        
        try:
            for i in range(max_polls):
                print(f"\n--- 第 {i+1} 次状态查询 ---")
                
                response = self.session.post(url, json=payload)
                print(f"请求URL: {url}")
                print(f"响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    data = result['data']
                    
                    print(f"✓ 状态查询成功")
                    print(f"  文件ID: {data['file_id']}")
                    print(f"  文件名: {data['file_name']}")
                    print(f"  RAG状态: {data['rag_status']}")
                    print(f"  上传状态: {data['upload_status']}")
                    print(f"  进度: {data['progress']}%")
                    
                    if data.get('doc_id'):
                        print(f"  文档ID: {data['doc_id']}")
                    
                    if data.get('error_message'):
                        print(f"  错误信息: {data['error_message']}")
                    
                    # 检查是否完成
                    if data['rag_status'] in ['completed', 'failed']:
                        print(f"✓ RAG处理已完成，状态: {data['rag_status']}")
                        return data['rag_status'] == 'completed'
                    
                    print(f"RAG处理中，{poll_interval}秒后再次查询...")
                    time.sleep(poll_interval)
                    
                else:
                    print(f"✗ 状态查询失败: {response.text}")
                    return False
            
            print(f"⚠ 达到最大轮询次数 {max_polls}，RAG处理可能仍在进行中")
            return False
            
        except Exception as e:
            print(f"✗ 请求异常: {e}")
            return False
    
    def run_complete_test(self):
        """运行完整的测试流程"""
        print("=" * 60)
        print("文件上传优化功能完整测试")
        print("=" * 60)
        
        # 1. 设置认证
        self.setup_auth()
        
        # 2. 创建测试文件
        filename = "test_optimization.pdf"
        file_size = 2048
        file_content = self.create_test_file(filename, file_size)
        print(f"✓ 创建测试文件: {filename}, 大小: {file_size} 字节")
        
        # 3. 获取预签名上传URL
        upload_info = self.test_get_upload_url(filename, file_size)
        if not upload_info:
            print("✗ 测试失败：无法获取预签名上传URL")
            return False
        
        file_id = upload_info['file_id']
        upload_url = upload_info['upload_url']
        
        # 4. 模拟文件上传
        etag = self.simulate_file_upload(upload_url, file_content)
        if not etag:
            print("✗ 测试失败：文件上传模拟失败")
            return False
        
        # 5. 测试优化后的确认上传接口
        if not self.test_confirm_upload(file_id, etag):
            print("✗ 测试失败：确认上传接口异常")
            return False
        
        # 6. 测试RAG状态查询接口
        if not self.test_rag_status(file_id):
            print("⚠ RAG状态查询测试未完全成功（可能仍在处理中）")
        
        print("\n" + "=" * 60)
        print("✓ 文件上传优化功能测试完成")
        print("=" * 60)
        return True


def main():
    """主函数"""
    # 检查服务是否运行
    base_url = "http://localhost:8000"
    
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print(f"✗ 服务健康检查失败: {response.status_code}")
            return
    except Exception as e:
        print(f"✗ 无法连接到服务 {base_url}: {e}")
        print("请确保服务正在运行")
        return
    
    # 运行测试
    tester = FileUploadOptimizationTester(base_url)
    tester.run_complete_test()


if __name__ == "__main__":
    main()
