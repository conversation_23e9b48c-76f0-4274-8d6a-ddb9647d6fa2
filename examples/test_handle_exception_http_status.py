#!/usr/bin/env python3
"""
测试 handle_exception 函数的HTTP状态码返回
验证 CLIENT_ERROR_STATUS 返回 400，FAIL_STATUS 返回 500
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.presentation.api.dependencies.api_common_utils import handle_exception, CLIENT_ERROR_STATUS, FAIL_STATUS
from src.domain.utils.check_utils import ClientException


def test_client_exception():
    """测试客户端异常返回400状态码"""
    print("=== 测试客户端异常 ===")
    
    # 创建一个客户端异常
    client_exception = ClientException(code="INVALID_PARAMETER", message="参数无效")
    
    # 调用handle_exception
    result = handle_exception(client_exception, "test-request-id")
    
    print(f"异常类型: ClientException")
    print(f"返回状态码: {result['status']}")
    print(f"期望状态码: {CLIENT_ERROR_STATUS}")
    print(f"状态码匹配: {result['status'] == CLIENT_ERROR_STATUS}")
    print(f"完整结果: {result}")
    print()


def test_file_content_too_large_error():
    """测试文件内容过大异常返回400状态码"""
    print("=== 测试文件内容过大异常 ===")
    
    try:
        # 导入文件内容过大异常
        from src.domain.services.session_service import FileContentTooLargeError
        
        # 创建一个文件内容过大异常
        file_exception = FileContentTooLargeError("文件内容过大")
        
        # 调用handle_exception
        result = handle_exception(file_exception, "test-request-id")
        
        print(f"异常类型: FileContentTooLargeError")
        print(f"返回状态码: {result['status']}")
        print(f"期望状态码: {CLIENT_ERROR_STATUS}")
        print(f"状态码匹配: {result['status'] == CLIENT_ERROR_STATUS}")
        print(f"完整结果: {result}")
        
    except ImportError:
        print("FileContentTooLargeError 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_file_processing_failed_exception():
    """测试文件处理失败异常返回400状态码"""
    print("=== 测试文件处理失败异常 ===")
    
    try:
        # 导入文件处理失败异常
        from src.domain.services.file_service import FileProcessingFailedException
        
        # 创建一个文件处理失败异常
        file_exception = FileProcessingFailedException("文件处理失败")
        
        # 调用handle_exception
        result = handle_exception(file_exception, "test-request-id")
        
        print(f"异常类型: FileProcessingFailedException")
        print(f"返回状态码: {result['status']}")
        print(f"期望状态码: {CLIENT_ERROR_STATUS}")
        print(f"状态码匹配: {result['status'] == CLIENT_ERROR_STATUS}")
        print(f"完整结果: {result}")
        
    except ImportError:
        print("FileProcessingFailedException 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_file_processing_timeout_exception():
    """测试文件处理超时异常返回400状态码"""
    print("=== 测试文件处理超时异常 ===")
    
    try:
        # 导入文件处理超时异常
        from src.domain.services.file_service import FileProcessingTimeoutException
        
        # 创建一个文件处理超时异常
        file_exception = FileProcessingTimeoutException("文件处理超时")
        
        # 调用handle_exception
        result = handle_exception(file_exception, "test-request-id")
        
        print(f"异常类型: FileProcessingTimeoutException")
        print(f"返回状态码: {result['status']}")
        print(f"期望状态码: {CLIENT_ERROR_STATUS}")
        print(f"状态码匹配: {result['status'] == CLIENT_ERROR_STATUS}")
        print(f"完整结果: {result}")
        
    except ImportError:
        print("FileProcessingTimeoutException 类不存在，跳过此测试")
    except Exception as e:
        print(f"测试失败: {e}")
    print()


def test_generic_exception():
    """测试通用异常返回500状态码"""
    print("=== 测试通用异常 ===")
    
    # 创建一个通用异常
    generic_exception = Exception("这是一个通用异常")
    
    # 调用handle_exception
    result = handle_exception(generic_exception, "test-request-id")
    
    print(f"异常类型: Exception")
    print(f"返回状态码: {result['status']}")
    print(f"期望状态码: {FAIL_STATUS}")
    print(f"状态码匹配: {result['status'] == FAIL_STATUS}")
    print(f"完整结果: {result}")
    print()


def test_status_constants():
    """测试状态常量定义"""
    print("=== 测试状态常量 ===")
    print(f"CLIENT_ERROR_STATUS: {CLIENT_ERROR_STATUS}")
    print(f"FAIL_STATUS: {FAIL_STATUS}")
    print(f"CLIENT_ERROR_STATUS == 400: {CLIENT_ERROR_STATUS == 400}")
    print(f"FAIL_STATUS == 500: {FAIL_STATUS == 500}")
    print()


def main():
    """主测试函数"""
    print("handle_exception HTTP状态码测试")
    print("=" * 50)
    
    # 测试状态常量
    test_status_constants()
    
    # 测试各种异常类型
    test_client_exception()
    test_file_content_too_large_error()
    test_file_processing_failed_exception()
    test_file_processing_timeout_exception()
    test_generic_exception()
    
    print("测试完成！")
    print("\n总结:")
    print("- CLIENT_ERROR_STATUS (400): 用于客户端错误，如参数无效、文件处理失败等")
    print("- FAIL_STATUS (500): 用于服务器内部错误，如未知异常等")


if __name__ == "__main__":
    main()
