#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos配置中心使用示例
演示如何在alpha-service项目中使用Nacos配置管理
"""
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


def basic_usage_example():
    """基本使用示例"""
    print("=== Nacos配置中心基本使用示例 ===")
    
    from shared.config.nacos_config import nacos_config_manager
    
    # 1. 获取完整配置
    print("\n1. 获取完整配置:")
    config = nacos_config_manager.get_config()
    print(f"配置内容: {config}")
    
    # 2. 获取特定配置值
    print("\n2. 获取特定配置值:")
    database_host = nacos_config_manager.get_config_value("database.host", "localhost")
    redis_port = nacos_config_manager.get_config_value("redis.port", 6379)
    app_name = nacos_config_manager.get_config_value("app.name", "alpha-service")
    
    print(f"数据库主机: {database_host}")
    print(f"Redis端口: {redis_port}")
    print(f"应用名称: {app_name}")
    
    # 3. 检查连接状态
    print(f"\n3. Nacos连接状态: {'已连接' if nacos_config_manager.is_connected() else '未连接'}")
    
    # 4. 获取连接信息
    print("\n4. 连接信息:")
    info = nacos_config_manager.get_connection_info()
    for key, value in info.items():
        print(f"  {key}: {value}")


def config_listener_example():
    """配置监听示例"""
    print("\n=== 配置监听示例 ===")
    
    from shared.config.nacos_config import nacos_config_manager
    
    # 配置变更计数器
    change_count = 0
    
    def on_config_changed(new_config):
        """配置变更回调函数"""
        nonlocal change_count
        change_count += 1
        print(f"配置变更 #{change_count}: 收到 {len(new_config)} 个配置项")
        
        # 处理特定配置变更
        if "database" in new_config:
            print(f"  数据库配置已更新: {new_config['database']}")
        if "redis" in new_config:
            print(f"  Redis配置已更新: {new_config['redis']}")
    
    # 添加配置监听器
    nacos_config_manager.add_config_listener(on_config_changed)
    print("配置监听器已添加")
    
    # 手动触发配置刷新来演示监听器
    print("手动刷新配置...")
    success = nacos_config_manager.refresh_config()
    print(f"配置刷新结果: {'成功' if success else '失败'}")
    
    # 等待一下看是否有回调
    time.sleep(1)
    print(f"总共检测到 {change_count} 次配置变更")


def fallback_config_example():
    """降级配置示例"""
    print("\n=== 降级配置示例 ===")
    
    from shared.config.nacos_config import nacos_config_manager
    
    # 设置降级配置
    fallback_config = {
        "database": {
            "host": "fallback-db.example.com",
            "port": 3306,
            "name": "alpha_service",
            "pool": {
                "min_size": 5,
                "max_size": 20
            }
        },
        "redis": {
            "host": "fallback-redis.example.com",
            "port": 6379,
            "db": 0,
            "timeout": 5
        },
        "app": {
            "name": "alpha-service",
            "version": "1.0.0",
            "debug": False
        },
        "features": {
            "new_algorithm": False,
            "cache_enabled": True,
            "log_level": "INFO"
        }
    }
    
    print("设置降级配置...")
    nacos_config_manager.set_fallback_config(fallback_config)
    
    # 获取配置（如果Nacos不可用，会使用降级配置）
    print("\n获取配置（可能使用降级配置）:")
    
    # 获取嵌套配置
    db_config = nacos_config_manager.get_config_value("database", {})
    redis_config = nacos_config_manager.get_config_value("redis", {})
    app_config = nacos_config_manager.get_config_value("app", {})
    
    print(f"数据库配置: {db_config}")
    print(f"Redis配置: {redis_config}")
    print(f"应用配置: {app_config}")
    
    # 获取深层嵌套配置
    db_pool_size = nacos_config_manager.get_config_value("database.pool.max_size", 10)
    feature_flag = nacos_config_manager.get_config_value("features.new_algorithm", False)
    
    print(f"数据库连接池最大大小: {db_pool_size}")
    print(f"新算法特性开关: {feature_flag}")


def multi_config_source_example():
    """多配置源示例"""
    print("\n=== 多配置源示例 ===")
    
    from shared.config.nacos_config import nacos_config_manager
    
    # 从不同的配置源获取配置
    config_sources = [
        ("wuying-alpha-service:application", "DEFAULT_GROUP"),
        ("feature-flags:config", "FEATURE_GROUP"),
        ("database:config", "DB_GROUP"),
        ("cache:config", "CACHE_GROUP")
    ]
    
    for data_id, group in config_sources:
        print(f"\n从配置源获取配置: {group}:{data_id}")
        try:
            config = nacos_config_manager.get_config(data_id=data_id, group=group)
            print(f"  配置内容: {config}")
            
            # 获取特定值
            if config:
                for key in list(config.keys())[:3]:  # 只显示前3个键
                    value = nacos_config_manager.get_config_value(
                        key=key, 
                        data_id=data_id, 
                        group=group
                    )
                    print(f"  {key}: {value}")
        except Exception as e:
            print(f"  获取配置失败: {e}")


class ConfigurableService:
    """可配置服务示例类"""
    
    def __init__(self):
        self.config = {}
        self.is_initialized = False
        self._load_config()
        self._setup_config_listener()
    
    def _load_config(self):
        """加载配置"""
        from shared.config.nacos_config import nacos_config_manager
        
        # 加载服务相关配置
        self.config = {
            "database": nacos_config_manager.get_config_value("database", {}),
            "redis": nacos_config_manager.get_config_value("redis", {}),
            "app": nacos_config_manager.get_config_value("app", {}),
            "features": nacos_config_manager.get_config_value("features", {})
        }
        
        print(f"[ConfigurableService] 配置已加载: {len(self.config)} 个配置组")
        self.is_initialized = True
    
    def _setup_config_listener(self):
        """设置配置监听"""
        from shared.config.nacos_config import nacos_config_manager
        
        nacos_config_manager.add_config_listener(self._on_config_changed)
        print("[ConfigurableService] 配置监听器已设置")
    
    def _on_config_changed(self, new_config):
        """配置变更处理"""
        old_config = self.config.copy()
        self._load_config()
        
        # 检查哪些配置发生了变更
        changed_sections = []
        for section in ["database", "redis", "app", "features"]:
            if old_config.get(section) != self.config.get(section):
                changed_sections.append(section)
        
        if changed_sections:
            print(f"[ConfigurableService] 配置变更检测: {', '.join(changed_sections)}")
            self._handle_config_change(changed_sections)
    
    def _handle_config_change(self, changed_sections):
        """处理配置变更"""
        for section in changed_sections:
            if section == "database":
                print("  - 重新初始化数据库连接")
            elif section == "redis":
                print("  - 重新初始化Redis连接")
            elif section == "app":
                print("  - 更新应用配置")
            elif section == "features":
                print("  - 更新特性开关")
    
    def get_database_config(self):
        """获取数据库配置"""
        return self.config.get("database", {})
    
    def is_feature_enabled(self, feature_name):
        """检查特性是否启用"""
        features = self.config.get("features", {})
        return features.get(feature_name, False)
    
    def get_app_info(self):
        """获取应用信息"""
        app_config = self.config.get("app", {})
        return {
            "name": app_config.get("name", "unknown"),
            "version": app_config.get("version", "1.0.0"),
            "debug": app_config.get("debug", False)
        }


def service_integration_example():
    """服务集成示例"""
    print("\n=== 服务集成示例 ===")
    
    # 创建可配置服务实例
    service = ConfigurableService()
    
    # 使用配置
    print("\n服务配置信息:")
    print(f"数据库配置: {service.get_database_config()}")
    print(f"应用信息: {service.get_app_info()}")
    print(f"新算法特性: {service.is_feature_enabled('new_algorithm')}")
    print(f"缓存特性: {service.is_feature_enabled('cache_enabled')}")
    
    # 模拟配置变更
    print("\n模拟配置变更...")
    from shared.config.nacos_config import nacos_config_manager
    nacos_config_manager.refresh_config()
    
    time.sleep(1)  # 等待配置变更处理


if __name__ == "__main__":
    print("🚀 Nacos配置中心使用示例")
    print("=" * 50)
    
    try:
        # 运行所有示例
        basic_usage_example()
        config_listener_example()
        fallback_config_example()
        multi_config_source_example()
        service_integration_example()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
