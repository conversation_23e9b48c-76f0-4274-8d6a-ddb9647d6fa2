#!/usr/bin/env python3
"""
HTTP状态码异常处理示例
演示如何使用 handle_exception_with_http_status 函数返回不同的HTTP状态码
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
from src.presentation.api.dependencies.api_common_utils import handle_exception_with_http_status, get_request_id_dependency
from src.domain.utils.check_utils import ClientException

app = FastAPI(title="HTTP状态码异常处理示例")


@app.get("/api/test-client-error")
async def test_client_error(request_id: str = Depends(get_request_id_dependency)):
    """测试客户端错误 - 应该返回400状态码"""
    try:
        # 模拟客户端错误
        raise ClientException(code="INVALID_PARAMETER", message="参数无效")
    except Exception as e:
        return handle_exception_with_http_status(e, request_id)


@app.get("/api/test-server-error")
async def test_server_error(request_id: str = Depends(get_request_id_dependency)):
    """测试服务器错误 - 应该返回500状态码"""
    try:
        # 模拟服务器内部错误
        raise Exception("这是一个服务器内部错误")
    except Exception as e:
        return handle_exception_with_http_status(e, request_id)


@app.get("/api/test-file-processing-error")
async def test_file_processing_error(request_id: str = Depends(get_request_id_dependency)):
    """测试文件处理错误 - 应该返回400状态码"""
    try:
        # 尝试导入文件处理异常类
        from src.domain.services.file_service import FileProcessingFailedException
        
        # 模拟文件处理失败
        raise FileProcessingFailedException("文件处理失败")
    except ImportError:
        # 如果类不存在，使用通用异常
        raise Exception("文件处理失败")
    except Exception as e:
        return handle_exception_with_http_status(e, request_id)


@app.get("/api/test-file-timeout-error")
async def test_file_timeout_error(request_id: str = Depends(get_request_id_dependency)):
    """测试文件处理超时错误 - 应该返回400状态码"""
    try:
        # 尝试导入文件处理超时异常类
        from src.domain.services.file_service import FileProcessingTimeoutException
        
        # 模拟文件处理超时
        raise FileProcessingTimeoutException("文件处理超时")
    except ImportError:
        # 如果类不存在，使用通用异常
        raise Exception("文件处理超时")
    except Exception as e:
        return handle_exception_with_http_status(e, request_id)


@app.get("/api/test-file-too-large-error")
async def test_file_too_large_error(request_id: str = Depends(get_request_id_dependency)):
    """测试文件内容过大错误 - 应该返回400状态码"""
    try:
        # 尝试导入文件内容过大异常类
        from src.domain.services.session_service import FileContentTooLargeError
        
        # 模拟文件内容过大
        raise FileContentTooLargeError("文件内容过大")
    except ImportError:
        # 如果类不存在，使用通用异常
        raise Exception("文件内容过大")
    except Exception as e:
        return handle_exception_with_http_status(e, request_id)


# 添加依赖注入的导入
from fastapi import Depends


if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动HTTP状态码异常处理示例服务器")
    print("=" * 50)
    print("测试端点:")
    print("  GET /api/test-client-error      - 客户端错误 (400)")
    print("  GET /api/test-server-error      - 服务器错误 (500)")
    print("  GET /api/test-file-processing-error - 文件处理错误 (400)")
    print("  GET /api/test-file-timeout-error - 文件超时错误 (400)")
    print("  GET /api/test-file-too-large-error - 文件过大错误 (400)")
    print()
    print("使用curl测试:")
    print("  curl -i http://localhost:8000/api/test-client-error")
    print("  curl -i http://localhost:8000/api/test-server-error")
    print()
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
