#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪 next_token 的 bug
详细分析每一步的数据流
"""
import requests
import json


def trace_next_token_bug():
    """追踪 next_token bug"""
    print("🔍 追踪 next_token bug")
    print("=" * 60)
    
    # 测试参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "742"
    page_size = 20
    
    print(f"📋 测试参数:")
    print(f"   session_id: {session_id}")
    print(f"   next_token: {next_token}")
    print(f"   page_size: {page_size}")
    print()
    
    # 发送请求
    base_url = "http://localhost:8000"
    params = {
        "session_id": session_id,
        "page_size": page_size,
        "next_token": next_token
    }
    
    headers = {
        "accept": "application/json",
        "content-type": "application/json"
    }
    
    print(f"🔄 发送请求到: {base_url}/api/sessions/query")
    print(f"   参数: {params}")
    print()
    
    try:
        response = requests.get(
            f"{base_url}/api/sessions/query",
            params=params,
            headers=headers,
            timeout=30
        )
        
        print(f"✅ 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"📄 完整响应:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 分析关键字段
            print(f"\n🔍 关键字段分析:")
            print(f"   code: {result.get('code')}")
            print(f"   success: {result.get('success')}")
            print(f"   next_token: {result.get('next_token')} (类型: {type(result.get('next_token'))})")
            print(f"   total_count: {result.get('total_count')}")
            
            data = result.get("data", {})
            events = data.get("events", [])
            print(f"   events 数量: {len(events)}")
            
            # 分析问题
            print(f"\n❌ 问题分析:")
            input_next_token = next_token
            output_next_token = result.get('next_token')
            
            print(f"   输入的 next_token: {input_next_token}")
            print(f"   输出的 next_token: {output_next_token}")
            
            if input_next_token == output_next_token:
                print(f"   🚨 问题确认: 输出的 next_token 和输入的一样！")
                print(f"   🚨 这表明 API 错误地返回了原始的 next_token")
                print(f"   🚨 正确的行为应该是：当没有更多数据时返回 null")
            else:
                print(f"   ✅ next_token 已正确更新")
            
            # 检查事件数量
            if len(events) < page_size:
                print(f"   📊 事件数量 ({len(events)}) < 页面大小 ({page_size})")
                print(f"   📊 这表明已经到达最后一页，next_token 应该是 null")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print(f"\n💡 解决方案建议:")
    print("1. 检查 SessionService.get_session_history 的返回值")
    print("2. 确保当 has_more=False 时，nextToken 应该是 None")
    print("3. 检查 package_api_result 是否正确处理 None 值")
    print("4. 可能需要在 API 层添加额外的逻辑来处理边界情况")


def test_different_scenarios():
    """测试不同场景"""
    print(f"\n🔧 测试不同场景:")
    print("-" * 40)
    
    base_url = "http://localhost:8000"
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    
    scenarios = [
        {
            "name": "无 next_token",
            "params": {
                "session_id": session_id,
                "page_size": 20
            }
        },
        {
            "name": "next_token=742",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "next_token": "742"
            }
        },
        {
            "name": "next_token=999",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "next_token": "999"
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}:")
        
        try:
            response = requests.get(
                f"{base_url}/api/sessions/query",
                params=scenario['params'],
                headers={"accept": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                input_token = scenario['params'].get('next_token', 'None')
                output_token = result.get('next_token', 'None')
                events_count = len(result.get('data', {}).get('events', []))
                
                print(f"   输入 next_token: {input_token}")
                print(f"   输出 next_token: {output_token}")
                print(f"   事件数量: {events_count}")
                
                if input_token == output_token and input_token != 'None':
                    print(f"   🚨 Bug 确认: next_token 未更新")
                else:
                    print(f"   ✅ next_token 处理正常")
                    
            else:
                print(f"   ❌ 失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")


if __name__ == "__main__":
    trace_next_token_bug()
    test_different_scenarios()
    
    print(f"\n{'='*60}")
    print("🎯 结论: next_token 的 bug 需要在 API 层面修复")
    print("建议在 SessionService.get_session_history 中添加逻辑")
    print("当 has_more=False 时，强制设置 nextToken=None")
    print("="*60)
