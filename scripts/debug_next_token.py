#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 next_token 问题
分析为什么 next_token 还是 742
"""
import sys
import os
import json
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.services.session_service import SessionService
from src.infrastructure.memory import memory_sdk


async def debug_next_token_issue():
    """调试 next_token 问题"""
    print("🔍 调试 next_token 问题")
    print("=" * 60)
    
    # 测试参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "742"
    page_size = 20
    
    print(f"📋 测试参数:")
    print(f"   session_id: {session_id}")
    print(f"   next_token: {next_token}")
    print(f"   page_size: {page_size}")
    print()
    
    # 1. 直接测试 Memory SDK
    print("🔧 1. 直接测试 Memory SDK:")
    print("-" * 40)
    
    try:
        memory_result = memory_sdk.list_events(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token,
            order_by="desc"
        )
        
        print(f"Memory SDK 返回结果:")
        if memory_result:
            events = memory_result.get("events", [])
            next_token_result = memory_result.get("next_token")
            has_more = memory_result.get("has_more")
            
            print(f"   事件数量: {len(events)}")
            print(f"   next_token: {next_token_result} (类型: {type(next_token_result)})")
            print(f"   has_more: {has_more} (类型: {type(has_more)})")
            
            # 显示原始结果
            print(f"\n原始结果 (JSON):")
            try:
                # 转换为可序列化的格式
                serializable_result = {}
                for key, value in memory_result.items():
                    if key == "events":
                        serializable_result[key] = f"[{len(value)} events]"
                    else:
                        serializable_result[key] = str(value)
                
                print(json.dumps(serializable_result, indent=2, ensure_ascii=False))
            except Exception as e:
                print(f"   序列化失败: {e}")
                print(f"   原始数据: {memory_result}")
        else:
            print("   Memory SDK 返回 None")
            
    except Exception as e:
        print(f"   Memory SDK 调用失败: {e}")
    
    # 2. 测试 SessionService.get_raw_events
    print(f"\n🔧 2. 测试 SessionService.get_raw_events:")
    print("-" * 40)
    
    try:
        # 创建 SessionService 实例
        session_service = SessionService()
        
        raw_events_result = session_service.get_raw_events(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token,
            order_by="desc"
        )
        
        print(f"get_raw_events 返回结果:")
        if raw_events_result:
            events = raw_events_result.get("events", [])
            next_token_result = raw_events_result.get("next_token")
            has_more = raw_events_result.get("has_more")
            
            print(f"   事件数量: {len(events)}")
            print(f"   next_token: {next_token_result} (类型: {type(next_token_result)})")
            print(f"   has_more: {has_more} (类型: {type(has_more)})")
        else:
            print("   get_raw_events 返回 None")
            
    except Exception as e:
        print(f"   get_raw_events 调用失败: {e}")
    
    # 3. 测试 SessionService.get_session_history
    print(f"\n🔧 3. 测试 SessionService.get_session_history:")
    print("-" * 40)
    
    try:
        session_history_result = session_service.get_session_history(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token
        )
        
        print(f"get_session_history 返回结果:")
        if session_history_result:
            next_token_result = session_history_result.get("nextToken")
            data = session_history_result.get("data", [])
            
            print(f"   nextToken: {next_token_result} (类型: {type(next_token_result)})")
            print(f"   data 长度: {len(data) if data else 0}")
            
            # 显示完整结果
            print(f"\n完整结果:")
            print(json.dumps({
                "nextToken": str(next_token_result),
                "data_length": len(data) if data else 0
            }, indent=2, ensure_ascii=False))
        else:
            print("   get_session_history 返回 None")
            
    except Exception as e:
        print(f"   get_session_history 调用失败: {e}")
    
    # 4. 分析问题
    print(f"\n🔍 4. 问题分析:")
    print("-" * 40)
    
    print("可能的问题原因:")
    print("1. Memory SDK 返回的 next_token 是字符串 'None'，但应该是 null")
    print("2. 分页逻辑可能有问题，当没有更多数据时应该返回 null")
    print("3. API 层可能错误地保留了原始的 next_token 值")
    print()
    
    print("建议的解决方案:")
    print("1. 检查 Memory SDK 的 list_events 实现")
    print("2. 在 SessionService 中添加 next_token 的处理逻辑")
    print("3. 确保当 has_more=False 时，next_token 应该是 None")


def main():
    """主函数"""
    asyncio.run(debug_next_token_issue())


if __name__ == "__main__":
    main()
