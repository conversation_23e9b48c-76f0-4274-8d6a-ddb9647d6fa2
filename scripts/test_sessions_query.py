#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 /sessions/query 接口
仿照 curl 命令请求会话历史记录
"""
import requests
import json
import urllib.parse
from datetime import datetime
import uuid


def test_sessions_query_api():
    """测试 sessions/query 接口"""
    print("=" * 60)
    print("测试 /sessions/query 接口")
    print("=" * 60)
    
    # 基础配置
    base_url = "http://localhost:8000"  # 本地开发服务器
    # base_url = "https://wuyingai-pre.cn-hangzhou.aliyuncs.com"  # 预发环境
    
    # 从 curl 命令中提取的参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "729"
    max_results = 100
    
    # 构建请求参数
    params = {
        "session_id": session_id,
        "page_size": max_results,
        "next_token": next_token
    }
    
    # 构建请求头（模拟浏览器）
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "origin": "http://localhost:3000",
        "referer": "http://localhost:3000/",
        # 认证相关（需要根据实际情况调整）
        "Authorization": "Bearer your_token_here",  # 需要替换为实际的token
        "X-Login-Token": "123",
        "X-Login-Session-Id": "123",
        "X-Region-Id": "cn-hangzhou"
    }
    
    print(f"📋 请求信息:")
    print(f"   URL: {base_url}/api/sessions/query")
    print(f"   方法: GET")
    print(f"   参数: {params}")
    print(f"   会话ID: {session_id}")
    print(f"   下一页令牌: {next_token}")
    print(f"   页面大小: {max_results}")
    print()
    
    try:
        print("🔄 发送请求...")
        
        # 发送 GET 请求
        response = requests.get(
            f"{base_url}/api/sessions/query",
            params=params,
            headers=headers,
            timeout=30
        )
        
        print(f"✅ 请求发送成功!")
        print(f"📊 响应信息:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print()
        
        # 解析响应
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"📄 响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 分析响应数据
                if isinstance(result, dict):
                    code = result.get("code", result.get("Code"))
                    msg = result.get("msg", result.get("Msg", result.get("message")))
                    data = result.get("data", result.get("Data"))
                    
                    print(f"\n📈 响应分析:")
                    print(f"   状态码: {code}")
                    print(f"   消息: {msg}")
                    
                    if data:
                        events = data.get("events", [])
                        next_token_result = result.get("next_token", result.get("NextToken"))
                        
                        print(f"   事件数量: {len(events) if events else 0}")
                        print(f"   下一页令牌: {next_token_result}")
                        
                        # 显示前几个事件
                        if events and len(events) > 0:
                            print(f"\n📝 前3个事件:")
                            for i, event in enumerate(events[:3]):
                                print(f"   事件 {i+1}: {event}")
                    else:
                        print(f"   数据: 无")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text[:500]}...")
                
        else:
            print(f"❌ 请求失败:")
            print(f"   状态码: {response.status_code}")
            print(f"   响应内容: {response.text[:500]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 其他异常: {e}")


def test_with_curl_style():
    """使用类似 curl 的方式测试"""
    print(f"\n🔧 使用 curl 风格测试:")
    print("-" * 40)
    
    # 构建类似原始 curl 的参数
    base_url = "http://localhost:8000"
    
    # 模拟原始 curl 的 form data 参数
    form_data = {
        "Action": "ListSessionHistoryMessage",
        "Timestamp": datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
        "SignatureNonce": f"x{uuid.uuid4().hex[:4]}",
        "ClientType": "HTML5",
        "Format": "JSON",
        "Version": "2025-07-17",
        "LoginToken": "123",
        "LoginSessionId": "123",
        "RegionId": "cn-hangzhou",
        "SessionId": "sess_68b5c3ad2ca84a9e875b8bcebc4261b0",
        "NextToken": "729",
        "MaxResults": "100"
    }
    
    # 转换为 URL 参数格式
    query_params = {
        "session_id": form_data["SessionId"],
        "page_size": int(form_data["MaxResults"]),
        "next_token": form_data["NextToken"]
    }
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Login-Token": form_data["LoginToken"],
        "X-Login-Session-Id": form_data["LoginSessionId"],
        "X-Region-Id": form_data["RegionId"],
        "X-Client-Type": form_data["ClientType"],
        "X-Format": form_data["Format"],
        "X-Version": form_data["Version"]
    }
    
    print(f"📋 curl 风格参数:")
    print(f"   原始参数: {form_data}")
    print(f"   转换后参数: {query_params}")
    print()
    
    try:
        print("🔄 发送 curl 风格请求...")
        
        response = requests.get(
            f"{base_url}/api/sessions/query",
            params=query_params,
            headers=headers,
            timeout=30
        )
        
        print(f"✅ curl 风格请求成功!")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应文本: {response.text[:200]}...")
        else:
            print(f"   错误响应: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ curl 风格请求失败: {e}")


def show_curl_command():
    """显示等效的 curl 命令"""
    print(f"\n📋 等效的 curl 命令:")
    print("-" * 40)
    
    base_url = "http://localhost:8000"
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "729"
    max_results = "100"
    
    curl_cmd = f"""curl '{base_url}/api/sessions/query?session_id={session_id}&page_size={max_results}&next_token={next_token}' \\
  -H 'accept: application/json, text/plain, */*' \\
  -H 'accept-language: zh-CN,zh;q=0.9' \\
  -H 'content-type: application/json' \\
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\
  -H 'X-Login-Token: 123' \\
  -H 'X-Login-Session-Id: 123' \\
  -H 'X-Region-Id: cn-hangzhou'"""
    
    print(curl_cmd)


def main():
    """主函数"""
    print("测试 /sessions/query 接口")
    
    # 基础测试
    test_sessions_query_api()
    
    # curl 风格测试
    test_with_curl_style()
    
    # 显示等效 curl 命令
    show_curl_command()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
