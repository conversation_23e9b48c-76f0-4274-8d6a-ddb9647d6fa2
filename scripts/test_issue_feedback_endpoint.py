#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试问题反馈 API 端点
验证新添加的问题反馈接口是否正常工作
"""
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.application.api_models import IssueFeedbackRequest, IssueFeedbackResponse


def test_issue_feedback_models():
    """测试问题反馈模型"""
    print("=" * 60)
    print("问题反馈 API 模型测试")
    print("=" * 60)
    
    # 测试请求模型
    print("\n🔧 测试请求模型:")
    print("-" * 40)
    
    # 测试1: 完整请求
    request_data = {
        "IssueDetail": "系统在处理大文件时会出现卡顿现象，希望能优化性能。具体表现为：\n1. 上传超过100MB的文件时响应缓慢\n2. 文件处理进度条不更新\n3. 偶尔会出现超时错误",
        "ContactInfo": "13800138000"
    }
    
    try:
        request = IssueFeedbackRequest(**request_data)
        print(f"✅ 完整请求创建成功:")
        print(f"   问题详情: {request.issue_detail[:50]}...")
        print(f"   联系方式: {request.contact_info}")
    except Exception as e:
        print(f"❌ 完整请求创建失败: {e}")
    
    # 测试2: 仅问题详情（联系方式可选）
    minimal_request_data = {
        "IssueDetail": "建议增加快捷键功能，提高操作效率"
    }
    
    try:
        minimal_request = IssueFeedbackRequest(**minimal_request_data)
        print(f"✅ 最小请求创建成功:")
        print(f"   问题详情: {minimal_request.issue_detail}")
        print(f"   联系方式: {minimal_request.contact_info}")
    except Exception as e:
        print(f"❌ 最小请求创建失败: {e}")
    
    # 测试3: 使用字段名而非别名
    field_request_data = {
        "issue_detail": "使用字段名创建的请求",
        "contact_info": "<EMAIL>"
    }
    
    try:
        field_request = IssueFeedbackRequest(**field_request_data)
        print(f"✅ 字段名请求创建成功:")
        print(f"   问题详情: {field_request.issue_detail}")
        print(f"   联系方式: {field_request.contact_info}")
    except Exception as e:
        print(f"❌ 字段名请求创建失败: {e}")


def test_issue_feedback_response():
    """测试问题反馈响应模型"""
    print("\n🔧 测试响应模型:")
    print("-" * 40)
    
    # 模拟成功响应
    response_data = {
        "Code": 200,
        "Msg": "反馈提交成功",
        "Data": {
            "FeedbackId": "feedback_abc123def456",
            "IssueDetail": "系统在处理大文件时会出现卡顿现象",
            "ContactInfo": "13800138000",
            "SubmitTime": datetime.now().isoformat(),
            "Status": "submitted"
        }
    }
    
    try:
        response = IssueFeedbackResponse(**response_data)
        print(f"✅ 响应模型创建成功:")
        print(f"   状态码: {response.code}")
        print(f"   消息: {response.msg}")
        print(f"   反馈ID: {response.data.feedback_id}")
        print(f"   状态: {response.data.status}")
        print(f"   提交时间: {response.data.submit_time}")
    except Exception as e:
        print(f"❌ 响应模型创建失败: {e}")


def test_validation_rules():
    """测试验证规则"""
    print("\n🔧 测试验证规则:")
    print("-" * 40)
    
    # 测试问题详情长度限制
    test_cases = [
        {
            "name": "空问题详情",
            "data": {"IssueDetail": ""},
            "should_fail": True
        },
        {
            "name": "正常长度问题详情",
            "data": {"IssueDetail": "这是一个正常长度的问题描述"},
            "should_fail": False
        },
        {
            "name": "最大长度问题详情",
            "data": {"IssueDetail": "x" * 500},
            "should_fail": False
        },
        {
            "name": "超长问题详情",
            "data": {"IssueDetail": "x" * 501},
            "should_fail": True
        },
        {
            "name": "超长联系方式",
            "data": {
                "IssueDetail": "正常问题",
                "ContactInfo": "x" * 101
            },
            "should_fail": True
        }
    ]
    
    for test_case in test_cases:
        try:
            request = IssueFeedbackRequest(**test_case["data"])
            if test_case["should_fail"]:
                print(f"❌ {test_case['name']}: 应该失败但成功了")
            else:
                print(f"✅ {test_case['name']}: 验证通过")
        except Exception as e:
            if test_case["should_fail"]:
                print(f"✅ {test_case['name']}: 正确拒绝 - {type(e).__name__}")
            else:
                print(f"❌ {test_case['name']}: 不应该失败 - {e}")


def test_json_serialization():
    """测试JSON序列化"""
    print("\n🔧 测试JSON序列化:")
    print("-" * 40)
    
    # 创建请求对象
    request = IssueFeedbackRequest(
        issue_detail="测试JSON序列化功能",
        contact_info="<EMAIL>"
    )
    
    try:
        # 序列化为JSON
        json_str = request.model_dump_json()
        print(f"✅ 请求序列化成功:")
        print(f"   JSON: {json_str}")
        
        # 从JSON反序列化
        json_data = json.loads(json_str)
        restored_request = IssueFeedbackRequest(**json_data)
        print(f"✅ 请求反序列化成功:")
        print(f"   问题详情: {restored_request.issue_detail}")
        print(f"   联系方式: {restored_request.contact_info}")
        
    except Exception as e:
        print(f"❌ JSON序列化测试失败: {e}")


def show_api_info():
    """显示API信息"""
    print("\n📋 API 接口信息:")
    print("-" * 40)
    print("接口路径: POST /api/sessions/issue/feedback")
    print("接口描述: 提交问题反馈接口")
    print("认证要求: 需要登录认证")
    print()
    print("请求参数:")
    print("  - IssueDetail (必填): 问题详情，1-500字符")
    print("  - ContactInfo (可选): 联系方式，最多100字符")
    print()
    print("响应格式:")
    print("  - Code: 状态码")
    print("  - Msg: 响应消息")
    print("  - Data: 响应数据")
    print("    - FeedbackId: 反馈ID")
    print("    - IssueDetail: 问题详情")
    print("    - ContactInfo: 联系方式")
    print("    - SubmitTime: 提交时间")
    print("    - Status: 处理状态")


def main():
    """主函数"""
    print("问题反馈 API 端点测试")
    print("=" * 60)
    
    # 运行所有测试
    test_issue_feedback_models()
    test_issue_feedback_response()
    test_validation_rules()
    test_json_serialization()
    show_api_info()
    
    print("\n" + "=" * 60)
    print("✅ 问题反馈 API 测试完成")
    print("✅ 所有模型和验证规则工作正常")
    print("✅ 接口已准备就绪，可以接收用户反馈")
    print("=" * 60)


if __name__ == "__main__":
    main()
