#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试 SessionService 的修复
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.services.session_service import SessionService


async def test_session_service_fix():
    """直接测试 SessionService 的修复"""
    print("🔧 直接测试 SessionService 修复")
    print("=" * 50)
    
    session_service = SessionService()
    
    # 测试参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "742"
    page_size = 20
    
    print(f"📋 测试参数:")
    print(f"   session_id: {session_id}")
    print(f"   next_token: {next_token}")
    print(f"   page_size: {page_size}")
    print()
    
    try:
        print("🔄 调用 SessionService.get_session_history...")
        
        result = await session_service.get_session_history(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token
        )
        
        print(f"✅ 调用成功!")
        print(f"📊 返回结果:")
        print(f"   类型: {type(result)}")
        print(f"   字段: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
        
        if isinstance(result, dict):
            next_token_result = result.get("nextToken")
            data = result.get("data", [])
            
            print(f"   nextToken: {next_token_result} (类型: {type(next_token_result)})")
            print(f"   data 长度: {len(data) if data else 0}")
            
            # 验证修复
            if next_token_result is None:
                print(f"   ✅ 修复成功: nextToken 正确返回 None")
            elif next_token_result == next_token:
                print(f"   ❌ 修复失败: nextToken 还是原值 {next_token}")
            else:
                print(f"   ⚠️  nextToken 已更新: {next_token} -> {next_token_result}")
                
            # 检查事件数量
            if len(data) < page_size:
                print(f"   📊 事件数 ({len(data)}) < 页面大小 ({page_size})")
                if next_token_result is None:
                    print(f"   ✅ 边界检查: 最后一页，nextToken 正确为 None")
                else:
                    print(f"   ❌ 边界检查: 最后一页，但 nextToken 不为 None")
        else:
            print(f"   ⚠️  返回结果不是字典: {result}")
            
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    await test_session_service_fix()
    
    print(f"\n{'='*50}")
    print("SessionService 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
