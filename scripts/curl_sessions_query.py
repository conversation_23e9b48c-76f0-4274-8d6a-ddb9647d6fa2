#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仿照 curl 命令测试 /sessions/query 接口
"""
import requests
import json
from datetime import datetime
import uuid


def test_sessions_query():
    """测试 sessions/query 接口"""
    print("🔄 测试 /sessions/query 接口")
    print("=" * 50)
    
    # 服务器配置
    base_url = "http://localhost:8000"
    
    # 从原始 curl 命令提取的参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "729"
    max_results = 100
    
    # 请求参数（GET 方式）
    params = {
        "session_id": session_id,
        "page_size": max_results,
        "next_token": next_token
    }
    
    # 请求头（模拟原始 curl）
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "content-type": "application/json",
        "user-agent": "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "origin": "http://localhost:3000",
        "referer": "http://localhost:3000/",
        # 认证头（从原始 curl 转换）
        "X-Login-Token": "123",
        "X-Login-Session-Id": "123", 
        "X-Region-Id": "cn-hangzhou"
    }
    
    print(f"📋 请求信息:")
    print(f"   URL: {base_url}/api/sessions/query")
    print(f"   SessionId: {session_id}")
    print(f"   NextToken: {next_token}")
    print(f"   PageSize: {max_results}")
    print()
    
    try:
        # 发送请求
        response = requests.get(
            f"{base_url}/api/sessions/query",
            params=params,
            headers=headers,
            timeout=30
        )
        
        print(f"✅ 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            # 格式化输出
            print(f"📄 响应数据:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 分析数据
            data = result.get("data", {})
            events = data.get("events", [])
            next_token_result = result.get("next_token")
            
            print(f"\n📊 数据分析:")
            print(f"   状态码: {result.get('code')}")
            print(f"   成功: {result.get('success')}")
            print(f"   事件数量: {len(events)}")
            print(f"   下一页令牌: {next_token_result}")
            print(f"   总数: {result.get('total_count', 0)}")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")


def show_equivalent_curl():
    """显示等效的 curl 命令"""
    print(f"\n📋 等效的 curl 命令:")
    print("-" * 50)
    
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "729"
    max_results = "100"
    
    curl_command = f"""curl 'http://localhost:8000/api/sessions/query?session_id={session_id}&page_size={max_results}&next_token={next_token}' \\
  -H 'accept: application/json, text/plain, */*' \\
  -H 'accept-language: zh-CN,zh;q=0.9' \\
  -H 'content-type: application/json' \\
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \\
  -H 'origin: http://localhost:3000' \\
  -H 'referer: http://localhost:3000/' \\
  -H 'X-Login-Token: 123' \\
  -H 'X-Login-Session-Id: 123' \\
  -H 'X-Region-Id: cn-hangzhou'"""
    
    print(curl_command)


def test_different_sessions():
    """测试不同的会话ID"""
    print(f"\n🔧 测试不同会话ID:")
    print("-" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "原始会话",
            "session_id": "sess_68b5c3ad2ca84a9e875b8bcebc4261b0",
            "next_token": "729"
        },
        {
            "name": "之前测试的会话",
            "session_id": "sess_368b1bf45ccf46daa8046cf31011eef1",
            "next_token": None
        },
        {
            "name": "不存在的会话",
            "session_id": "sess_nonexistent_12345",
            "next_token": None
        }
    ]
    
    base_url = "http://localhost:8000"
    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "X-Login-Token": "123",
        "X-Login-Session-Id": "123",
        "X-Region-Id": "cn-hangzhou"
    }
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}:")
        
        params = {
            "session_id": test_case["session_id"],
            "page_size": 20
        }
        
        if test_case["next_token"]:
            params["next_token"] = test_case["next_token"]
        
        try:
            response = requests.get(
                f"{base_url}/api/sessions/query",
                params=params,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                events = result.get("data", {}).get("events", [])
                print(f"   ✅ 成功 - 事件数: {len(events)}")
            else:
                print(f"   ❌ 失败 - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")


if __name__ == "__main__":
    # 主测试
    test_sessions_query()
    
    # 显示等效 curl 命令
    show_equivalent_curl()
    
    # 测试不同会话
    test_different_sessions()
    
    print(f"\n{'='*50}")
    print("✅ 测试完成")
