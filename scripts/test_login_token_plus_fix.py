#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LoginToken中+号的处理
验证修改后的代码能正确处理包含+的token
"""
import sys
import os
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.services.auth_service import AuthService


def create_mock_request(query_string: str, query_params: dict = None):
    """创建模拟的FastAPI Request对象"""
    mock_request = Mock()
    
    # 模拟URL对象
    mock_url = Mock()
    mock_url.query = query_string
    mock_request.url = mock_url
    
    # 模拟query_params
    mock_query_params = Mock()
    if query_params:
        for key, value in query_params.items():
            mock_query_params.get = Mock(side_effect=lambda k, default=None: query_params.get(k, default))
    else:
        mock_query_params.get = Mock(return_value=None)
    mock_request.query_params = mock_query_params
    
    # 模拟headers
    mock_headers = Mock()
    mock_headers.get = Mock(return_value=None)
    mock_request.headers = mock_headers
    
    return mock_request


def test_raw_login_token_extraction():
    """测试原始LoginToken提取功能"""
    print("🔧 测试原始LoginToken提取功能")
    print("=" * 60)
    
    auth_service = AuthService()
    
    test_cases = [
        {
            "name": "包含+号的token",
            "query_string": "LoginToken=abc+def+ghi&other=value",
            "expected": "abc+def+ghi",
            "description": "token中的+号应该保持不变"
        },
        {
            "name": "包含%20编码的token",
            "query_string": "LoginToken=abc%20def%20ghi&other=value",
            "expected": "abc%20def%20ghi",
            "description": "URL编码应该保持原样"
        },
        {
            "name": "普通token",
            "query_string": "LoginToken=normaltoken123&other=value",
            "expected": "normaltoken123",
            "description": "普通token应该正常提取"
        },
        {
            "name": "小写loginToken",
            "query_string": "loginToken=lowercase+token&other=value",
            "expected": "lowercase+token",
            "description": "小写参数名也应该支持"
        },
        {
            "name": "复杂token",
            "query_string": "LoginToken=eyJhbGciOiJIUzI1NiIs+InR5cCI6IkpXVCJ9&RegionId=cn-hangzhou",
            "expected": "eyJhbGciOiJIUzI1NiIs+InR5cCI6IkpXVCJ9",
            "description": "复杂token中的+号应该保持"
        },
        {
            "name": "没有LoginToken",
            "query_string": "other=value&another=test",
            "expected": None,
            "description": "没有LoginToken时应该返回None"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   查询字符串: {test_case['query_string']}")
        print(f"   期望结果: {test_case['expected']}")
        print(f"   描述: {test_case['description']}")
        
        # 创建模拟请求
        mock_request = create_mock_request(test_case['query_string'])
        
        try:
            # 调用私有方法测试
            result = auth_service._get_raw_login_token(mock_request)
            
            print(f"   实际结果: {result}")
            
            if result == test_case['expected']:
                print(f"   ✅ 测试通过")
            else:
                print(f"   ❌ 测试失败")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")


async def test_extract_auth_params():
    """测试完整的认证参数提取"""
    print(f"\n🔧 测试完整的认证参数提取")
    print("-" * 40)
    
    auth_service = AuthService()
    
    test_cases = [
        {
            "name": "原始查询字符串提取成功",
            "query_string": "LoginToken=token+with+plus&LoginSessionId=session123&RegionId=cn-hangzhou",
            "query_params": {},  # 模拟query_params为空或不同
            "expected_token": "token+with+plus"
        },
        {
            "name": "回退到query_params",
            "query_string": "",  # 空查询字符串
            "query_params": {
                "LoginToken": "token with spaces",  # 模拟+被解码为空格
                "LoginSessionId": "session123"
            },
            "expected_token": "token with spaces"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 完整测试 {i}: {test_case['name']}")
        
        # 创建模拟请求
        mock_request = create_mock_request(
            test_case['query_string'], 
            test_case['query_params']
        )
        
        # 设置query_params的get方法
        if test_case['query_params']:
            def mock_get(key, default=None):
                return test_case['query_params'].get(key, default)
            mock_request.query_params.get = mock_get
        
        try:
            # 调用完整的提取方法
            login_token, login_session_id, region_id = await auth_service._extract_auth_params(mock_request)
            
            print(f"   提取的LoginToken: {login_token}")
            print(f"   期望的LoginToken: {test_case['expected_token']}")
            
            if login_token == test_case['expected_token']:
                print(f"   ✅ 完整测试通过")
            else:
                print(f"   ❌ 完整测试失败")
                
        except Exception as e:
            print(f"   ❌ 完整测试异常: {e}")


def show_fix_summary():
    """显示修复总结"""
    print(f"\n📋 修复总结:")
    print("-" * 40)
    print("🔧 问题:")
    print("   - request.query_params.get('LoginToken') 会将URL中的+解码为空格")
    print("   - 导致原本包含+的token被错误修改")
    print()
    print("🔧 解决方案:")
    print("   - 添加 _get_raw_login_token() 方法")
    print("   - 直接从原始查询字符串中提取token")
    print("   - 保持+号不被解码")
    print("   - 如果原始提取失败，回退到默认方式")
    print()
    print("🔧 修改的文件:")
    print("   - src/domain/services/auth_service.py")
    print("     * 添加 _get_raw_login_token() 方法")
    print("     * 修改 _extract_auth_params() 方法")
    print()
    print("✅ 优势:")
    print("   - 保持token原始性")
    print("   - 向后兼容")
    print("   - 支持大小写驼峰命名")
    print("   - 有回退机制")


async def main():
    """主函数"""
    show_fix_summary()
    
    # 测试原始token提取
    test_raw_login_token_extraction()
    
    # 测试完整的参数提取
    await test_extract_auth_params()
    
    print(f"\n{'='*60}")
    print("✅ LoginToken +号处理修复测试完成")
    print("✅ 现在可以正确处理包含+号的token")
    print("="*60)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
