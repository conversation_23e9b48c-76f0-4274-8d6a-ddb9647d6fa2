#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 Memory SDK 的 has_more 返回值
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.memory import memory_sdk


def debug_memory_sdk_has_more():
    """调试 Memory SDK 的 has_more 返回值"""
    print("🔍 调试 Memory SDK 的 has_more 返回值")
    print("=" * 60)
    
    # 测试参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "742"
    page_size = 20
    
    print(f"📋 测试参数:")
    print(f"   session_id: {session_id}")
    print(f"   next_token: {next_token}")
    print(f"   page_size: {page_size}")
    print()
    
    try:
        print("🔄 调用 memory_sdk.list_events...")
        
        result = memory_sdk.list_events(
            session_id=session_id,
            page_size=page_size,
            next_token=next_token,
            order_by="desc"
        )
        
        print(f"✅ Memory SDK 调用成功!")
        
        if result:
            events = result.get("events", [])
            next_token_result = result.get("next_token")
            has_more = result.get("has_more")
            
            print(f"📊 Memory SDK 返回结果:")
            print(f"   事件数量: {len(events)}")
            print(f"   next_token: {next_token_result} (类型: {type(next_token_result)})")
            print(f"   has_more: {has_more} (类型: {type(has_more)})")
            
            # 分析问题
            print(f"\n🔍 问题分析:")
            print(f"   输入 next_token: {next_token}")
            print(f"   输出 next_token: {next_token_result}")
            print(f"   事件数量: {len(events)}")
            print(f"   页面大小: {page_size}")
            print(f"   has_more: {has_more}")
            
            # 逻辑检查
            if len(events) < page_size:
                print(f"\n📊 逻辑检查:")
                print(f"   事件数 ({len(events)}) < 页面大小 ({page_size})")
                print(f"   这通常意味着已经到达最后一页")
                
                if has_more:
                    print(f"   🚨 问题: has_more=True，但事件数小于页面大小")
                    print(f"   🚨 这可能是 Memory SDK 的 bug")
                else:
                    print(f"   ✅ has_more=False，符合预期")
                    
                if next_token_result is None:
                    print(f"   ✅ next_token=None，符合预期")
                else:
                    print(f"   🚨 问题: next_token 不为 None")
            else:
                print(f"\n📊 逻辑检查:")
                print(f"   事件数 ({len(events)}) = 页面大小 ({page_size})")
                print(f"   可能还有更多数据")
                
                if has_more:
                    print(f"   ✅ has_more=True，可能正确")
                else:
                    print(f"   ⚠️  has_more=False，但页面已满")
            
            # 显示原始数据类型
            print(f"\n🔧 原始数据类型检查:")
            print(f"   has_more 原始值: {repr(has_more)}")
            print(f"   next_token 原始值: {repr(next_token_result)}")
            
            # 检查是否是字符串类型的布尔值
            if isinstance(has_more, str):
                print(f"   🚨 has_more 是字符串类型: '{has_more}'")
                if has_more.lower() == 'true':
                    print(f"   🚨 字符串 'true' 被当作 True")
                elif has_more.lower() == 'false':
                    print(f"   ✅ 字符串 'false' 应该被当作 False")
            elif isinstance(has_more, bool):
                print(f"   ✅ has_more 是正确的布尔类型")
            else:
                print(f"   ⚠️  has_more 是其他类型: {type(has_more)}")
                
        else:
            print(f"❌ Memory SDK 返回 None")
            
    except Exception as e:
        print(f"❌ Memory SDK 调用失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n💡 修复建议:")
    print("1. 如果 has_more 是字符串，需要在 SessionService 中转换为布尔值")
    print("2. 如果事件数 < 页面大小，应该强制设置 has_more=False")
    print("3. 当 has_more=False 时，next_token 应该设置为 None")


if __name__ == "__main__":
    debug_memory_sdk_has_more()
