#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 next_token bug 修复
验证修复后的行为是否正确
"""
import requests
import json
import time


def test_next_token_fix():
    """测试 next_token 修复"""
    print("🔧 测试 next_token bug 修复")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    
    # 测试场景
    test_cases = [
        {
            "name": "测试最后一页（应该返回 null）",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "next_token": "742"  # 这个应该是最后一页
            },
            "expected_next_token": None
        },
        {
            "name": "测试第一页（无 next_token）",
            "params": {
                "session_id": session_id,
                "page_size": 5  # 小页面，可能有更多数据
            },
            "expected_next_token": "not_null"  # 期望不是 null
        },
        {
            "name": "测试不存在的 next_token",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "next_token": "999999"  # 不存在的 token
            },
            "expected_next_token": None
        }
    ]
    
    headers = {
        "accept": "application/json",
        "content-type": "application/json"
    }
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   参数: {test_case['params']}")
        
        try:
            # 发送请求
            response = requests.get(
                f"{base_url}/api/sessions/query",
                params=test_case['params'],
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 分析结果
                input_token = test_case['params'].get('next_token', 'None')
                output_token = result.get('next_token')
                events_count = len(result.get('data', {}).get('events', []))
                page_size = test_case['params']['page_size']
                
                print(f"   ✅ 请求成功")
                print(f"   📊 结果分析:")
                print(f"      输入 next_token: {input_token}")
                print(f"      输出 next_token: {output_token}")
                print(f"      事件数量: {events_count}")
                print(f"      页面大小: {page_size}")
                
                # 验证修复
                if test_case['expected_next_token'] is None:
                    if output_token is None:
                        print(f"   ✅ 修复验证: next_token 正确返回 null")
                    else:
                        print(f"   ❌ 修复失败: 期望 null，实际 {output_token}")
                elif test_case['expected_next_token'] == "not_null":
                    if output_token is not None:
                        print(f"   ✅ 修复验证: next_token 正确返回非 null 值")
                    else:
                        print(f"   ⚠️  注意: next_token 为 null（可能是正常的）")
                
                # 检查边界情况
                if events_count < page_size:
                    if output_token is None:
                        print(f"   ✅ 边界检查: 事件数 < 页面大小，next_token 正确为 null")
                    else:
                        print(f"   ❌ 边界检查: 事件数 < 页面大小，但 next_token 不为 null")
                
                # 检查是否还是原始 bug
                if input_token != 'None' and input_token == output_token and events_count < page_size:
                    print(f"   🚨 Bug 仍存在: next_token 未更新且已到最后一页")
                
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                if response.status_code == 401:
                    print(f"      需要认证，使用 curl 测试")
                    test_with_curl(test_case)
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print(f"\n{'='*60}")
    print("🎯 修复验证完成")


def test_with_curl(test_case):
    """使用 curl 测试（带认证）"""
    import subprocess
    
    params = test_case['params']
    session_id = params['session_id']
    page_size = params['page_size']
    next_token = params.get('next_token', '')
    
    # 构建 curl 命令
    url = f"http://localhost:8000/api/sessions/query?LoginToken=1&LoginSession=1&RegionId=1&session_id={session_id}&page_size={page_size}"
    if next_token:
        url += f"&next_token={next_token}"
    
    curl_cmd = [
        'curl', '-X', 'GET', url,
        '-H', 'Content-Type: application/json',
        '-H', 'Authorization: Bearer YOUR_ACCESS_TOKEN'
    ]
    
    try:
        print(f"   🔄 使用 curl 测试...")
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            try:
                response_data = json.loads(result.stdout)
                output_token = response_data.get('next_token')
                events_count = len(response_data.get('data', {}).get('events', []))
                
                print(f"   ✅ curl 成功")
                print(f"      输出 next_token: {output_token}")
                print(f"      事件数量: {events_count}")
                
                # 验证修复
                if test_case['expected_next_token'] is None and output_token is None:
                    print(f"   ✅ curl 验证: next_token 正确返回 null")
                elif test_case['expected_next_token'] is None and output_token is not None:
                    print(f"   ❌ curl 验证: 期望 null，实际 {output_token}")
                    
            except json.JSONDecodeError:
                print(f"   ❌ curl 响应解析失败")
        else:
            print(f"   ❌ curl 失败: {result.stderr}")
            
    except Exception as e:
        print(f"   ❌ curl 异常: {e}")


def show_fix_summary():
    """显示修复总结"""
    print(f"\n📋 修复总结:")
    print("-" * 40)
    print("🔧 修复内容:")
    print("   1. 在 SessionService.get_session_history 中添加了 next_token 处理逻辑")
    print("   2. 当 has_more=False 或 next_token=None 时，强制返回 null")
    print("   3. 添加了详细的调试日志")
    print()
    print("🎯 修复目标:")
    print("   1. 解决最后一页返回原始 next_token 的 bug")
    print("   2. 确保分页逻辑的正确性")
    print("   3. 提升用户体验")
    print()
    print("✅ 预期效果:")
    print("   1. 最后一页时 next_token 返回 null")
    print("   2. 前端不会进入无限循环")
    print("   3. 分页行为符合预期")


if __name__ == "__main__":
    show_fix_summary()
    test_next_token_fix()
    
    print(f"\n💡 如果测试失败，请确保:")
    print("1. 服务器正在运行")
    print("2. 使用正确的认证参数")
    print("3. 检查服务器日志中的调试信息")
