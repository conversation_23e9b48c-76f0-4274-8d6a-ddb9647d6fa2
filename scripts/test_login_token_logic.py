#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的登录令牌验证逻辑
验证根据 loginToken 长度决定是否走真正的登录校验
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.popclients.login_verify_client import LoginVerifyClient


def test_login_token_length_logic():
    """测试登录令牌长度逻辑"""
    print("🔧 测试登录令牌长度逻辑")
    print("=" * 60)
    
    # 创建客户端
    client = LoginVerifyClient()
    
    # 测试用例
    test_cases = [
        {
            "name": "短令牌 (应该使用mock)",
            "login_token": "123",  # 3个字符，<= 50
            "expected_mock": True
        },
        {
            "name": "50字符令牌 (应该使用mock)",
            "login_token": "a" * 50,  # 正好50个字符，<= 50
            "expected_mock": True
        },
        {
            "name": "51字符令牌 (应该使用真实验证)",
            "login_token": "a" * 51,  # 51个字符，> 50
            "expected_mock": False
        },
        {
            "name": "长令牌 (应该使用真实验证)",
            "login_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",  # JWT token，> 50
            "expected_mock": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"   令牌长度: {len(test_case['login_token'])}")
        print(f"   期望使用mock: {test_case['expected_mock']}")
        
        try:
            # 测试同步验证
            print(f"   🔄 测试同步验证...")
            response = client.verify_login_token(
                login_token=test_case['login_token'],
                login_session_id="test_session",
                region_id="cn-hangzhou"
            )
            
            # 检查响应
            if response and response.body:
                print(f"   ✅ 同步验证成功")
                print(f"      响应成功: {response.body.success}")
                
                # 提取用户信息
                user_info = client.get_user_info_from_response(response)
                if user_info:
                    print(f"      用户信息: ali_uid={user_info.get('ali_uid')}, wy_id={user_info.get('wy_id')}")
                    
                    # 判断是否是mock用户
                    is_mock_user = user_info.get('ali_uid') == 1550203943326350 and user_info.get('wy_id') == 'test_user'
                    print(f"      实际使用mock: {is_mock_user}")
                    
                    if is_mock_user == test_case['expected_mock']:
                        print(f"   ✅ 逻辑正确: 符合预期")
                    else:
                        print(f"   ❌ 逻辑错误: 期望mock={test_case['expected_mock']}, 实际mock={is_mock_user}")
                else:
                    print(f"   ❌ 无法提取用户信息")
            else:
                print(f"   ❌ 同步验证失败")
                
        except Exception as e:
            print(f"   ❌ 同步验证异常: {e}")


async def test_async_login_token_logic():
    """测试异步登录令牌逻辑"""
    print(f"\n🔧 测试异步登录令牌逻辑")
    print("-" * 40)
    
    client = LoginVerifyClient()
    
    test_cases = [
        {
            "name": "短令牌异步 (应该使用mock)",
            "login_token": "async123",  # 8个字符，<= 50
            "expected_mock": True
        },
        {
            "name": "长令牌异步 (应该使用真实验证)",
            "login_token": "a" * 60,  # 60个字符，> 50
            "expected_mock": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 异步测试 {i}: {test_case['name']}")
        print(f"   令牌长度: {len(test_case['login_token'])}")
        
        try:
            # 测试异步验证
            print(f"   🔄 测试异步验证...")
            response = await client.verify_login_token_async(
                login_token=test_case['login_token'],
                login_session_id="async_test_session",
                region_id="cn-hangzhou"
            )
            
            if response and response.body:
                print(f"   ✅ 异步验证成功")
                user_info = client.get_user_info_from_response(response)
                if user_info:
                    is_mock_user = user_info.get('ali_uid') == 1550203943326350
                    print(f"      实际使用mock: {is_mock_user}")
                    
                    if is_mock_user == test_case['expected_mock']:
                        print(f"   ✅ 异步逻辑正确")
                    else:
                        print(f"   ❌ 异步逻辑错误")
            else:
                print(f"   ❌ 异步验证失败")
                
        except Exception as e:
            print(f"   ❌ 异步验证异常: {e}")


def show_logic_summary():
    """显示逻辑总结"""
    print(f"\n📋 修改后的逻辑总结:")
    print("-" * 40)
    print("🔧 新的判断逻辑:")
    print("   - 如果 loginToken 长度 <= 50 字符：使用 mock 用户")
    print("   - 如果 loginToken 长度 > 50 字符：使用真实登录验证")
    print()
    print("🎯 适用场景:")
    print("   - 开发测试：使用简短的 token (如 '123', 'test')")
    print("   - 生产环境：使用真实的 JWT token (通常 > 50 字符)")
    print()
    print("✅ 优势:")
    print("   - 不依赖环境变量或配置文件")
    print("   - 根据 token 本身的特征自动判断")
    print("   - 简化开发和测试流程")


async def main():
    """主函数"""
    show_logic_summary()
    
    # 测试同步逻辑
    test_login_token_length_logic()
    
    # 测试异步逻辑
    await test_async_login_token_logic()
    
    print(f"\n{'='*60}")
    print("✅ 登录令牌长度逻辑测试完成")
    print("✅ 新逻辑：根据 token 长度自动选择验证方式")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
