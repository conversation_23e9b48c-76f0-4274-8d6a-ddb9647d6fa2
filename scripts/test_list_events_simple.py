#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试 MemorySDK 的 list_events 方法
"""
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.memory import memory_sdk


def test_list_events():
    """测试 list_events 方法"""
    print("=" * 50)
    print("测试 MemorySDK list_events 方法")
    print("=" * 50)
    
    # 使用提供的参数
    session_id = "sess_68b5c3ad2ca84a9e875b8bcebc4261b0"
    next_token = "729"
    page_size = 100
    order_by = "desc"
    
    print(f"📋 测试参数:")
    print(f"   SessionId: {session_id}")
    print(f"   NextToken: {next_token}")
    print(f"   PageSize: {page_size}")
    print(f"   OrderBy: {order_by}")
    print()
    
    try:
        print("🔄 调用 memory_sdk.list_events...")
        
        result = memory_sdk.list_events(
            session_id=session_id,
            page_size=page_size,
            order_by=order_by,
            next_token=next_token
        )
        
        print(f"✅ 调用成功!")
        print(f"📊 返回结果:")
        
        if result is None:
            print("   结果: None")
        elif isinstance(result, dict):
            print(f"   类型: dict")
            print(f"   字段: {list(result.keys())}")
            
            # 显示事件信息
            events = result.get("events", [])
            print(f"   事件数量: {len(events)}")
            
            # 显示分页信息
            next_token_result = result.get("next_token")
            has_more = result.get("has_more", False)
            print(f"   下一页令牌: {next_token_result}")
            print(f"   是否有更多: {has_more}")
            
            # 显示所有事件的基本信息
            if events:
                print(f"\n📝 所有事件列表:")
                for i, event in enumerate(events):
                    print(f"   事件 {i+1}:")

                    # 尝试获取事件属性
                    try:
                        if hasattr(event, 'event_id'):
                            print(f"     ID: {event.event_id}")
                        if hasattr(event, 'type'):
                            event_type = event.type
                            if hasattr(event_type, 'value'):
                                event_type = event_type.value
                            print(f"     类型: {event_type}")
                        if hasattr(event, 'timestamp'):
                            print(f"     时间: {event.timestamp}")
                        if hasattr(event, 'content'):
                            content = str(event.content)
                            if len(content) > 200:
                                content = content[:200] + "..."
                            print(f"     内容: {content}")
                        if hasattr(event, 'session_id'):
                            print(f"     会话ID: {event.session_id}")
                        if hasattr(event, 'round_id'):
                            print(f"     轮次ID: {event.round_id}")
                    except Exception as e:
                        print(f"     解析事件失败: {e}")
                        print(f"     原始数据: {str(event)[:200]}...")
                    print()  # 空行分隔

            # 显示完整的原始结果
            print(f"\n📄 完整原始结果:")
            try:
                # 尝试将结果转换为可读格式
                def convert_to_serializable(obj):
                    """将对象转换为可序列化的格式"""
                    if hasattr(obj, '__dict__'):
                        result = {}
                        for key, value in obj.__dict__.items():
                            if hasattr(value, 'value'):  # 处理枚举
                                result[key] = value.value
                            elif hasattr(value, '__dict__'):  # 处理嵌套对象
                                result[key] = convert_to_serializable(value)
                            else:
                                result[key] = str(value)
                        return result
                    elif isinstance(obj, list):
                        return [convert_to_serializable(item) for item in obj]
                    elif isinstance(obj, dict):
                        return {k: convert_to_serializable(v) for k, v in obj.items()}
                    else:
                        return str(obj)

                serializable_result = convert_to_serializable(result)
                print(json.dumps(serializable_result, indent=2, ensure_ascii=False))

            except Exception as e:
                print(f"   JSON序列化失败: {e}")
                print(f"   直接打印原始结果:")
                print(f"   {result}")
        else:
            print(f"   类型: {type(result)}")
            print(f"   内容: {str(result)[:200]}...")
            
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        import traceback
        print(f"详细错误:")
        traceback.print_exc()


def test_memory_sdk_status():
    """检查 MemorySDK 状态"""
    print(f"\n🔧 检查 MemorySDK 状态:")
    print("-" * 30)
    
    try:
        print(f"MemorySDK 对象: {memory_sdk}")
        print(f"类型: {type(memory_sdk)}")
        
        if hasattr(memory_sdk, '_memory'):
            print(f"内部 Memory 对象: {memory_sdk._memory}")
            print(f"Memory 类型: {type(memory_sdk._memory)}")
        
        # 检查可用方法
        methods = [m for m in dir(memory_sdk) if not m.startswith('_') and callable(getattr(memory_sdk, m))]
        print(f"可用方法: {methods}")
        
    except Exception as e:
        print(f"检查状态失败: {e}")


def main():
    """主函数"""
    print("MemorySDK list_events 简单测试")
    
    # 检查状态
    test_memory_sdk_status()
    
    # 测试 list_events
    test_list_events()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
