#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 MemorySDK 的 list_events 方法
根据提供的参数测试事件列表查询功能
"""
import sys
import os
import json
from datetime import datetime
from typing import Optional, List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.infrastructure.memory import memory_sdk
from loguru import logger


def test_list_events_basic():
    """基础测试 list_events 方法"""
    print("=" * 60)
    print("MemorySDK list_events 基础测试")
    print("=" * 60)
    
    # 从URL参数中提取的测试参数
    session_id = "sess_368b1bf45ccf46daa8046cf31011eef1"
    next_token = "666"
    page_size = 100
    order_by = "desc"
    
    print(f"\n🔧 测试参数:")
    print(f"   SessionId: {session_id}")
    print(f"   NextToken: {next_token}")
    print(f"   PageSize: {page_size}")
    print(f"   OrderBy: {order_by}")
    
    try:
        print(f"\n📋 调用 memory_sdk.list_events...")
        result = memory_sdk.list_events(
            session_id=session_id,
            page_size=page_size,
            order_by=order_by,
            next_token=next_token
        )
        
        if result is not None:
            print(f"✅ list_events 调用成功")
            print(f"   结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print(f"   返回字段: {list(result.keys())}")
                
                # 显示事件列表信息
                events = result.get("events", [])
                print(f"   事件数量: {len(events)}")
                
                # 显示分页信息
                next_token_result = result.get("next_token")
                has_more = result.get("has_more", False)
                print(f"   下一页令牌: {next_token_result}")
                print(f"   是否有更多: {has_more}")
                
                # 显示前几个事件的基本信息
                if events:
                    print(f"\n📝 前3个事件信息:")
                    for i, event in enumerate(events[:3]):
                        print(f"   事件 {i+1}:")
                        if hasattr(event, '__dict__'):
                            event_dict = event.__dict__
                        elif isinstance(event, dict):
                            event_dict = event
                        else:
                            event_dict = {"raw": str(event)}
                        
                        # 显示事件的关键字段
                        for key in ['event_id', 'type', 'timestamp', 'session_id', 'content']:
                            if key in event_dict:
                                value = event_dict[key]
                                if key == 'content' and isinstance(value, str) and len(value) > 50:
                                    value = value[:50] + "..."
                                print(f"     {key}: {value}")
                else:
                    print(f"   📭 没有找到事件数据")
            else:
                print(f"   ⚠️  返回结果不是字典格式: {result}")
        else:
            print(f"❌ list_events 返回 None")
            
    except Exception as e:
        print(f"❌ list_events 调用失败: {e}")
        logger.error(f"测试失败: {e}")


def test_list_events_with_different_params():
    """测试不同参数组合"""
    print(f"\n🔧 测试不同参数组合:")
    print("-" * 40)
    
    session_id = "sess_368b1bf45ccf46daa8046cf31011eef1"
    
    test_cases = [
        {
            "name": "默认参数",
            "params": {
                "session_id": session_id
            }
        },
        {
            "name": "小页面大小",
            "params": {
                "session_id": session_id,
                "page_size": 5,
                "order_by": "desc"
            }
        },
        {
            "name": "升序排列",
            "params": {
                "session_id": session_id,
                "page_size": 10,
                "order_by": "asc"
            }
        },
        {
            "name": "带next_token",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "order_by": "desc",
                "next_token": "666"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}:")
        try:
            result = memory_sdk.list_events(**test_case['params'])
            if result:
                events_count = len(result.get("events", []))
                has_more = result.get("has_more", False)
                next_token = result.get("next_token")
                print(f"   ✅ 成功 - 事件数: {events_count}, 有更多: {has_more}, 下一页: {next_token}")
            else:
                print(f"   ❌ 返回 None")
        except Exception as e:
            print(f"   ❌ 失败: {e}")


def test_list_events_with_filters():
    """测试带过滤条件的查询"""
    print(f"\n🔧 测试事件类型过滤:")
    print("-" * 40)
    
    session_id = "sess_368b1bf45ccf46daa8046cf31011eef1"
    
    filter_test_cases = [
        {
            "name": "包含特定事件类型",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "include_event_types": ["message", "tool_call"]
            }
        },
        {
            "name": "排除特定事件类型",
            "params": {
                "session_id": session_id,
                "page_size": 20,
                "exclude_event_types": ["system", "debug"]
            }
        }
    ]
    
    for test_case in filter_test_cases:
        print(f"\n📋 {test_case['name']}:")
        try:
            result = memory_sdk.list_events(**test_case['params'])
            if result:
                events = result.get("events", [])
                print(f"   ✅ 成功 - 过滤后事件数: {len(events)}")
                
                # 显示事件类型分布
                if events:
                    event_types = {}
                    for event in events:
                        event_type = getattr(event, 'type', 'unknown')
                        if hasattr(event_type, 'value'):
                            event_type = event_type.value
                        event_types[event_type] = event_types.get(event_type, 0) + 1
                    
                    print(f"   事件类型分布: {event_types}")
            else:
                print(f"   ❌ 返回 None")
        except Exception as e:
            print(f"   ❌ 失败: {e}")


def test_edge_cases():
    """测试边界情况"""
    print(f"\n🔧 测试边界情况:")
    print("-" * 40)
    
    edge_cases = [
        {
            "name": "无效session_id",
            "params": {
                "session_id": "",
                "page_size": 10
            }
        },
        {
            "name": "不存在的session_id",
            "params": {
                "session_id": "sess_nonexistent_12345",
                "page_size": 10
            }
        },
        {
            "name": "负数页面大小",
            "params": {
                "session_id": "sess_368b1bf45ccf46daa8046cf31011eef1",
                "page_size": -1
            }
        },
        {
            "name": "零页面大小",
            "params": {
                "session_id": "sess_368b1bf45ccf46daa8046cf31011eef1",
                "page_size": 0
            }
        },
        {
            "name": "超大页面大小",
            "params": {
                "session_id": "sess_368b1bf45ccf46daa8046cf31011eef1",
                "page_size": 10000
            }
        }
    ]
    
    for test_case in edge_cases:
        print(f"\n📋 {test_case['name']}:")
        try:
            result = memory_sdk.list_events(**test_case['params'])
            if result:
                events_count = len(result.get("events", []))
                print(f"   ✅ 处理成功 - 事件数: {events_count}")
            else:
                print(f"   ⚠️  返回 None (可能是预期行为)")
        except Exception as e:
            print(f"   ❌ 异常: {e}")


def show_memory_sdk_info():
    """显示 MemorySDK 信息"""
    print(f"\n📋 MemorySDK 信息:")
    print("-" * 40)
    
    try:
        # 检查 memory_sdk 是否已初始化
        if hasattr(memory_sdk, '_memory') and memory_sdk._memory:
            print(f"✅ MemorySDK 已初始化")
            print(f"   Memory对象类型: {type(memory_sdk._memory)}")
        else:
            print(f"❌ MemorySDK 未初始化")
        
        # 显示可用方法
        methods = [method for method in dir(memory_sdk) if not method.startswith('_')]
        print(f"   可用方法: {', '.join(methods)}")
        
    except Exception as e:
        print(f"❌ 获取MemorySDK信息失败: {e}")


def main():
    """主函数"""
    print("MemorySDK list_events 方法测试")
    print("=" * 60)
    
    # 显示 MemorySDK 信息
    show_memory_sdk_info()
    
    # 运行基础测试
    test_list_events_basic()
    
    # 运行不同参数测试
    test_list_events_with_different_params()
    
    # 运行过滤测试
    test_list_events_with_filters()
    
    # 运行边界情况测试
    test_edge_cases()
    
    print("\n" + "=" * 60)
    print("✅ list_events 方法测试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
