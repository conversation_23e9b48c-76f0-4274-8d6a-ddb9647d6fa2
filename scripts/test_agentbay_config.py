#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 AgentBay 配置项
验证 agentbay_aliuid 配置在各个环境中是否正确加载
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.shared.config.environments import env_manager, Environment


def test_agentbay_config():
    """测试 AgentBay 配置"""
    print("=" * 60)
    print("AgentBay 配置测试")
    print("=" * 60)
    
    # 测试所有环境
    environments = [Environment.DAILY, Environment.PRE, Environment.PROD]
    
    for env in environments:
        print(f"\n🔧 测试 {env.value.upper()} 环境:")
        print("-" * 40)
        
        # 切换到指定环境
        env_manager.switch_environment(env)
        config = env_manager.get_config()
        
        # 获取 AgentBay 配置
        cloud_resource_id = config.agentbay_cloud_resource_id
        aliuid = config.agentbay_aliuid
        
        print(f"agentbay_cloud_resource_id: {cloud_resource_id}")
        print(f"agentbay_aliuid: {aliuid}")
        
        # 验证配置是否为空
        if not cloud_resource_id:
            print("⚠️  警告: agentbay_cloud_resource_id 为空")
        else:
            print("✅ agentbay_cloud_resource_id 配置正常")
            
        if not aliuid:
            print("⚠️  警告: agentbay_aliuid 为空")
        else:
            print("✅ agentbay_aliuid 配置正常")
    
    print("\n" + "=" * 60)
    print("✅ AgentBay 配置测试完成")
    print("=" * 60)


def test_config_access_methods():
    """测试配置访问方法"""
    print("\n🔍 测试配置访问方法:")
    print("-" * 40)
    
    # 使用环境管理器的方法
    config = env_manager.get_config()
    
    # 方法1: 通过属性访问
    aliuid_via_property = config.agentbay_aliuid
    print(f"通过属性访问: {aliuid_via_property}")
    
    # 方法2: 通过 get_config_value 方法
    aliuid_via_method = env_manager.get_config_value("agentbay_aliuid", "default_value")
    print(f"通过方法访问: {aliuid_via_method}")
    
    # 方法3: 直接从 settings 访问
    aliuid_via_settings = env_manager.settings.get("agentbay_aliuid", "not_found")
    print(f"直接从settings访问: {aliuid_via_settings}")
    
    # 验证三种方法的结果是否一致
    if aliuid_via_property == aliuid_via_method == aliuid_via_settings:
        print("✅ 所有访问方法结果一致")
    else:
        print("❌ 访问方法结果不一致")


def show_all_agentbay_configs():
    """显示所有 AgentBay 相关配置"""
    print("\n📋 所有 AgentBay 相关配置:")
    print("-" * 40)

    # 直接获取 AgentBay 配置
    config = env_manager.get_config()
    agentbay_configs = {
        "agentbay_cloud_resource_id": config.agentbay_cloud_resource_id,
        "agentbay_aliuid": config.agentbay_aliuid
    }

    for key, value in agentbay_configs.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    # 显示当前环境信息
    env_info = env_manager.get_environment_info()
    print(f"当前环境: {env_info['current_environment']}")
    
    # 运行测试
    test_agentbay_config()
    test_config_access_methods()
    show_all_agentbay_configs()
