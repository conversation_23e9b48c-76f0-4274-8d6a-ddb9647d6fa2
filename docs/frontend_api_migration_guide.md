# 文件上传接口改造说明 - 前端迁移指南

## 📋 改造背景

由于文件上传确认接口中的 RAG 解析处理时间过长（通常需要几十秒到几分钟），经常导致前端请求超时，影响用户体验。为了解决这个问题，我们对相关接口进行了优化改造。

## 🔄 接口变更概览

| 接口名称 | 变更类型 | 说明 |
|---------|---------|------|
| `ConfirmPresignedUpload` | **行为变更** | 立即返回，不再等待 RAG 解析完成 |
| `GetFileAnalyzeStatus` | **新增接口** | 用于查询文件 RAG 解析状态和进度 |

## 📝 详细接口说明

### 1. ConfirmPresignedUpload 接口变更

#### 接口信息
- **路径**: `POST /api/files/confirm-upload`
- **变更**: 行为优化，立即返回

#### 请求参数（无变化）
```json
{
  "file_id": "123",
  "etag": "d41d8cd98f00b204e9800998ecf8427e"
}
```

#### 响应变更对比

**🔴 改造前响应**（需要等待几十秒）:
```json
{
  "code": "200",
  "success": true,
  "message": "上传确认成功，已处理完成",
  "data": {
    "file_id": "123",
    "status": "completed",  // 直接返回完成状态
    "message": "上传确认成功，已处理完成"
  }
}
```

**🟢 改造后响应**（秒级返回）:
```json
{
  "code": "200",
  "success": true,
  "message": "上传确认成功，RAG解析已启动",
  "data": {
    "file_id": "123",
    "status": "analyzing",  // 返回解析中状态
    "message": "上传确认成功，RAG解析已启动，请使用rag-status接口查询处理进度"
  }
}
```

#### 关键变更点
1. **响应时间**: 从几十秒优化到秒级返回
2. **状态值**: 从 `completed` 改为 `analyzing`
3. **消息提示**: 引导使用新的状态查询接口

### 2. GetFileAnalyzeStatus 接口（新增）

#### 接口信息
- **路径**: `POST /api/files/rag-status`
- **用途**: 查询文件 RAG 解析状态和进度
- **调用方式**: 轮询调用

#### 请求参数
```json
{
  "file_id": "123"
}
```

#### 响应格式
```json
{
  "code": "200",
  "success": true,
  "message": "RAG状态查询成功",
  "data": {
    "file_id": "123",
    "file_name": "document.pdf",
    "rag_status": "analyzing",        // RAG解析状态
    "upload_status": "analyzing",     // 文件上传状态
    "progress": 65,                   // 处理进度 0-100
    "doc_id": null,                   // 文档ID（完成后才有）
    "error_message": null,            // 错误信息（失败时才有）
    "started_at": "2024-01-15T10:30:00Z",
    "completed_at": null              // 完成时间（完成后才有）
  }
}
```

#### 状态值说明

| rag_status | 含义 | 前端处理建议 |
|------------|------|-------------|
| `waiting` | 等待上传完成 | 显示"等待上传完成" |
| `analyzing` | 正在RAG解析 | 显示进度条和"解析中" |
| `completed` | 解析完成 | 停止轮询，显示"解析完成" |
| `failed` | 解析失败 | 停止轮询，显示错误信息 |

## 🔧 前端改造方案

### 方案一：渐进式改造（推荐）

保持现有逻辑基本不变，只需要添加轮询逻辑：

```javascript
class FileUploadService {
  // 原有的确认上传方法
  async confirmUpload(fileId, etag) {
    const response = await fetch('/api/files/confirm-upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ file_id: fileId, etag })
    });
    
    const result = await response.json();
    
    if (result.success && result.data.status === 'analyzing') {
      // 新逻辑：开始轮询状态
      return this.pollAnalyzeStatus(fileId);
    }
    
    // 兼容旧逻辑（如果后端还有其他状态）
    return result;
  }
  
  // 新增：轮询解析状态
  async pollAnalyzeStatus(fileId, maxAttempts = 60, interval = 2000) {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const statusResult = await this.getAnalyzeStatus(fileId);
        
        if (statusResult.success) {
          const { rag_status, progress, error_message, doc_id } = statusResult.data;
          
          // 触发进度更新事件
          this.onProgressUpdate?.(progress, rag_status);
          
          if (rag_status === 'completed') {
            return { success: true, data: { ...statusResult.data, status: 'completed' } };
          }
          
          if (rag_status === 'failed') {
            return { success: false, error: error_message || '解析失败' };
          }
          
          // 继续轮询
          await this.sleep(interval);
        }
      } catch (error) {
        console.warn(`状态查询失败 (第${attempt}次):`, error);
        if (attempt === maxAttempts) {
          return { success: false, error: '状态查询超时' };
        }
        await this.sleep(interval);
      }
    }
    
    return { success: false, error: '解析超时' };
  }
  
  // 新增：获取解析状态
  async getAnalyzeStatus(fileId) {
    const response = await fetch('/api/files/rag-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ file_id: fileId })
    });
    
    return await response.json();
  }
  
  // 工具方法
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // 进度回调（可选）
  onProgressUpdate = null;
}
```

### 方案二：完全重构

如果希望更清晰的代码结构：

```javascript
class FileUploadService {
  async confirmUpload(fileId, etag) {
    // 第一步：确认上传
    const confirmResult = await this.callConfirmUpload(fileId, etag);
    if (!confirmResult.success) {
      throw new Error(confirmResult.message || '确认上传失败');
    }
    
    // 第二步：等待解析完成
    const analyzeResult = await this.waitForAnalyzeComplete(fileId);
    return analyzeResult;
  }
  
  async callConfirmUpload(fileId, etag) {
    const response = await fetch('/api/files/confirm-upload', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ file_id: fileId, etag })
    });
    
    return await response.json();
  }
  
  async waitForAnalyzeComplete(fileId) {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const result = await this.getAnalyzeStatus(fileId);
          
          if (result.success) {
            const { rag_status, progress, error_message } = result.data;
            
            // 更新UI进度
            this.updateProgress?.(progress, rag_status);
            
            if (rag_status === 'completed') {
              resolve(result);
            } else if (rag_status === 'failed') {
              reject(new Error(error_message || '解析失败'));
            } else {
              // 继续轮询
              setTimeout(poll, 2000);
            }
          } else {
            reject(new Error('状态查询失败'));
          }
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  }
}
```

## 🎨 UI/UX 改进建议

### 1. 进度显示
```javascript
// 在轮询过程中更新进度条
onProgressUpdate: (progress, status) => {
  const progressBar = document.getElementById('upload-progress');
  const statusText = document.getElementById('status-text');
  
  progressBar.style.width = `${progress}%`;
  
  const statusMessages = {
    'waiting': '等待上传完成...',
    'analyzing': `正在解析文档... ${progress}%`,
    'completed': '解析完成！',
    'failed': '解析失败'
  };
  
  statusText.textContent = statusMessages[status] || '处理中...';
}
```

### 2. 错误处理
```javascript
// 友好的错误提示
const handleUploadError = (error) => {
  const errorMessages = {
    '状态查询超时': '文档解析时间较长，请稍后在文件列表中查看结果',
    '解析失败': '文档格式可能不支持，请尝试其他格式',
    '网络错误': '网络连接异常，请检查网络后重试'
  };
  
  const friendlyMessage = errorMessages[error.message] || '上传过程中出现问题，请重试';
  showNotification(friendlyMessage, 'error');
};
```

## ⚠️ 注意事项

### 1. 兼容性处理
- 改造期间可能存在新旧版本并存，建议根据响应状态判断使用哪种逻辑
- 保留原有错误处理逻辑，确保向后兼容

### 2. 轮询策略
- **建议间隔**: 2-3秒（避免过于频繁）
- **超时时间**: 5-10分钟（根据业务需求调整）
- **错误重试**: 网络错误时可重试2-3次

### 3. 用户体验
- 显示明确的进度指示
- 提供取消操作选项
- 长时间处理时给出友好提示

### 4. 性能考虑
- 页面关闭时清理轮询定时器
- 避免同时对多个文件进行密集轮询

## 🧪 测试建议

### 1. 功能测试
- 正常上传流程测试
- 网络异常情况测试
- 长时间处理场景测试

### 2. 边界测试
- 轮询超时处理
- 服务端错误响应处理
- 并发上传测试

## 📞 技术支持

如有疑问，请联系后端开发团队：
- 接口文档：[内部文档链接]
- 技术群：[群聊链接]
- 紧急联系：[联系方式]

---

**更新时间**: 2024-01-15  
**版本**: v1.0  
**状态**: 待前端确认
