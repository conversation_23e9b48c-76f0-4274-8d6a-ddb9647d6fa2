#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 Nacos 配置获取测试 - 复现之前的 hello:f 测试
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def simple_nacos_test():
    """简单的 Nacos 测试，和之前获取 hello:f 一样"""
    print("=" * 60)
    print("🚀 简单 Nacos 配置获取测试")
    print("=" * 60)
    
    try:
        # 1. 使用原生 Nacos 客户端
        print("\n1️⃣ 原生 Nacos 客户端测试:")
        print("-" * 40)
        
        import nacos
        from shared.config.environments import env_manager
        
        config = env_manager.get_config()
        endpoint = config.nacos_endpoint
        
        # 解析endpoint
        if "://" in endpoint:
            from urllib.parse import urlparse
            parsed = urlparse(endpoint)
            server_addresses = f"{parsed.hostname}:{parsed.port or 8848}"
        else:
            server_addresses = endpoint
        
        print(f"📡 连接: {server_addresses}")
        
        client = nacos.NacosClient(
            server_addresses=server_addresses,
            namespace="",
            username="",
            password=""
        )
        
        # 测试 ai_ppt 配置
        print(f"\n🔍 获取 wuying-alpha-service:ai_ppt:")
        content = client.get_config(
            data_id="wuying-alpha-service:ai_ppt",
            group="DEFAULT_GROUP",
            timeout=5
        )
        
        print(f"   返回类型: {type(content)}")
        print(f"   返回值: {repr(content)}")
        
        if content:
            print(f"   长度: {len(content)} 字符")
            print(f"   内容: {content}")
        else:
            print(f"   ❌ 内容为空或None")
        
        # 2. 使用 NacosConfigManager
        print(f"\n2️⃣ NacosConfigManager 测试:")
        print("-" * 40)
        
        from shared.config.nacos_config import nacos_config_manager
        
        # 清除缓存
        nacos_config_manager._config_cache.clear()
        print("🧹 缓存已清除")
        
        print(f"\n🔍 获取 wuying-alpha-service:ai_ppt:")
        config = nacos_config_manager.get_config(
            data_id="wuying-alpha-service:ai_ppt",
            group="DEFAULT_GROUP"
        )
        
        print(f"   返回类型: {type(config)}")
        print(f"   返回值: {repr(config)}")
        
        if config:
            print(f"   配置项数: {len(config) if isinstance(config, dict) else 'N/A'}")
            
            # 转换为 JSON 字符串看看
            import json
            json_str = json.dumps(config, ensure_ascii=False)
            print(f"   JSON字符串: {json_str}")
        else:
            print(f"   ❌ 配置为空")
        
        # 3. 测试 ppt 和 ppt2
        print(f"\n3️⃣ 测试 ppt 和 ppt2:")
        print("-" * 40)
        
        for test_data_id in ["wuying-alpha-service:ppt", "wuying-alpha-service:ppt2"]:
            print(f"\n🔍 测试 {test_data_id}:")
            
            # 原生客户端
            native_content = client.get_config(
                data_id=test_data_id,
                group="DEFAULT_GROUP",
                timeout=5
            )
            print(f"   原生客户端: {repr(native_content)}")
            
            # ConfigManager
            manager_config = nacos_config_manager.get_config(
                data_id=test_data_id,
                group="DEFAULT_GROUP"
            )
            print(f"   ConfigManager: {repr(manager_config)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单 Nacos 测试")
    
    success = simple_nacos_test()
    
    print(f"\n{'='*60}")
    print(f"🏁 测试完成: {'成功' if success else '失败'}")
    print(f"{'='*60}")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
