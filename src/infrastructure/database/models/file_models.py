# -*- coding: utf-8 -*-
"""
文件相关的数据库模型
"""
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Optional
from sqlalchemy import Column, BigInteger, String, DateTime, Text, Integer
from sqlalchemy.orm import relationship

# 导入共享的Base
from .session_models import Base
from src.domain.utils.time_utils import get_current_time




class ArtifactType(Enum):
    """制品类型枚举"""
    AI_PPT = "aippt"  # AI PPT 
    CONTENT_STRING = "content_string"  # 纯文本制品
    URL_LINK = "url_link"  # 链接

class FileType(Enum):
    """文件类型枚举"""
    # RESULT_ARTIFACT = "resultArtifact"  # 结果制品
    # PROCESS_ARTIFACT = "processArtifact"  # 过程制品
    ARTIFACT_FILE = "artifactFile"  # 制品文件
    SESSION_FILE = "sessionFile"  # 会话文件


class UploadStatus(Enum):
    """上传状态枚举"""
    UPLOADING = "uploading"  # 上传中
    ANALYZING = "analyzing"  # 分析中（RAG解析中）
    COMPLETED = "completed"  # 上传完成
    FAILED = "failed"  # 上传失败


class AlphaFile(Base):
    """
    Alpha文件表模型
    用于存储文件信息，包括OSS存储路径等
    """
    __tablename__ = "alpha_files"
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment="文件ID")
    
    # 基本信息
    title = Column(String(255), nullable=False, comment="文件名")
    oss_bucket = Column(String(128), nullable=False, comment="OSS存储桶")
    oss_object_name = Column(String(512), nullable=False, comment="OSS对象名称")
    
    # 关联信息
    session_id = Column(String(64), nullable=True, comment="来源会话ID")
    ali_uid = Column(BigInteger, nullable=True, comment="阿里云用户ID")
    wy_id = Column(String(64), nullable=True, comment="wy用户ID")
    artifact_id = Column(String(64), nullable=True, comment="制品ID，用于标识制品文件")
    
    # 文件类型
    type = Column(String(32), nullable=False, comment="文件类型: resultArtifact/processArtifact/sessionFile")
    
    # 扩展信息
    file_size = Column(BigInteger, nullable=True, comment="文件大小（字节）")
    content_type = Column(String(128), nullable=True, comment="文件MIME类型")
    content = Column(Text, nullable=True, comment="文件内容")
    doc_id = Column(String(128), nullable=True, comment="RAG解析返回的文档ID")
    upload_status = Column(String(32), default=UploadStatus.UPLOADING.value, comment="上传状态: uploading/completed/failed")
    upload_progress = Column(Integer, default=0, comment="上传进度（0-100）")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 软删除标识
    is_delete = Column(Integer, default=0, nullable=False, comment="是否删除：0-未删除，1-已删除")
    
    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, nullable=False, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, nullable=False, comment="修改时间")
    
    def __repr__(self):
        return f"<AlphaFile(id={self.id}, title='{self.title}', type='{self.type}')>"
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "file_id": str(self.id),
            "title": self.title,
            "oss_bucket": self.oss_bucket,
            "oss_object_name": self.oss_object_name,
            "session_id": self.session_id,
            "ali_uid": self.ali_uid,
            "wy_id": self.wy_id,
            "artifact_id": self.artifact_id,
            "type": self.type,
            "file_size": self.file_size,
            "content_type": self.content_type,
            "content": self.content,
            "doc_id": self.doc_id,
            "upload_status": self.upload_status,
            "upload_progress": self.upload_progress,
            "error_message": self.error_message,
            "is_delete": self.is_delete,
            "gmt_created": self.gmt_created.isoformat() if self.gmt_created else None,
            "gmt_modified": self.gmt_modified.isoformat() if self.gmt_modified else None
        }
    
    @property
    def file_size_formatted(self) -> str:
        """格式化文件大小"""
        if not self.file_size:
            return "0B"
        
        size = self.file_size
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{size}{units[unit_index]}"
        else:
            return f"{size:.1f}{units[unit_index]}"
    
    @property
    def url(self) -> str:
        """生成文件访问URL"""
        return f"/api/files/download/{self.id}"
    
    def update_upload_progress(self, progress: int, status: Optional[str] = None, error_message: Optional[str] = None):
        """更新上传进度"""
        self.upload_progress = progress
        if status:
            self.upload_status = status
        if error_message:
            self.error_message = error_message
        self.gmt_modified = get_current_time()
    
    def mark_completed(self, file_size: Optional[int] = None):
        """标记为上传完成"""
        self.upload_status = UploadStatus.COMPLETED.value
        self.upload_progress = 100
        if file_size is not None:
            self.file_size = file_size
        self.gmt_modified = get_current_time()
    
    def mark_failed(self, error_message: str):
        """标记为上传失败"""
        self.upload_status = UploadStatus.FAILED.value
        self.error_message = error_message
        self.gmt_modified = get_current_time()
