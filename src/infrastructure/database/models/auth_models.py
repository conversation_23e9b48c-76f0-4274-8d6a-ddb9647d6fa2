#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鉴权相关的数据模型
"""
from datetime import datetime, timezone, timedelta
from enum import Enum
from sqlalchemy import Column, Integer, BigInteger, String, DateTime, Boolean, Text, ForeignKey, Index
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from typing import Optional, Dict, Any

Base = declarative_base()

# 导入时间工具函数
from src.domain.utils.time_utils import get_current_time


class ResourceType(Enum):
    """资源类型枚举"""
    FILE = "file"                    # 文件
    SESSION = "session"              # 会话
    KNOWLEDGE_BASE = "knowledge_base" # 知识库

    @classmethod
    def get_all_types(cls) -> list[str]:
        """获取所有资源类型的字符串值"""
        return [resource_type.value for resource_type in cls]

    @classmethod
    def is_valid_type(cls, resource_type: str) -> bool:
        """检查资源类型是否有效"""
        return resource_type in cls.get_all_types()

    @classmethod
    def get_display_name(cls, resource_type: str) -> str:
        """获取资源类型的显示名称"""
        display_names = {
            cls.FILE.value: "文件",
            cls.SESSION.value: "会话",
            cls.KNOWLEDGE_BASE.value: "知识库",
        }
        return display_names.get(resource_type, resource_type)

    def __str__(self) -> str:
        """返回枚举值的字符串表示"""
        return self.value


class PermissionType(Enum):
    """权限类型枚举"""
    READ = "read"        # 读取权限
    WRITE = "write"      # 写入权限
    DELETE = "delete"    # 删除权限
    SHARE = "share"      # 分享权限
    ADMIN = "admin"      # 管理权限

    @classmethod
    def get_all_permissions(cls) -> list[str]:
        """获取所有权限类型的字符串值"""
        return [permission.value for permission in cls]

    @classmethod
    def is_valid_permission(cls, permission: str) -> bool:
        """检查权限类型是否有效"""
        return permission in cls.get_all_permissions()

    @classmethod
    def get_display_name(cls, permission: str) -> str:
        """获取权限类型的显示名称"""
        display_names = {
            cls.READ.value: "读取",
            cls.WRITE.value: "写入",
            cls.DELETE.value: "删除",
            cls.SHARE.value: "分享",
            cls.ADMIN.value: "管理"
        }
        return display_names.get(permission, permission)

    @classmethod
    def get_owner_permissions(cls) -> list[str]:
        """获取资源所有者的所有权限"""
        return [cls.READ.value, cls.WRITE.value, cls.DELETE.value, cls.SHARE.value, cls.ADMIN.value]

    @classmethod
    def get_public_permissions(cls) -> list[str]:
        """获取公开资源的权限"""
        return [cls.READ.value]

    def __str__(self) -> str:
        """返回枚举值的字符串表示"""
        return self.value


class ShareType(Enum):
    """分享类型枚举"""
    PUBLIC = "public"          # 公开分享
    PRIVATE = "private"        # 私有分享（需要密码或邀请）
    TEAM = "team"              # 团队内分享


# 移除用户表，直接使用 ali_uid 和 wy_id 标识用户


class Team(Base):
    """团队模型"""
    __tablename__ = 'auth_teams'

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(128), nullable=False, comment="团队名称")
    description = Column(Text, nullable=True, comment="团队描述")

    # 创建者（使用 ali_uid + wy_id）
    creator_ali_uid = Column(BigInteger, nullable=False, comment="创建者阿里云ID")
    creator_wy_id = Column(String(64), nullable=False, comment="创建者网易ID")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_deleted = Column(Boolean, default=False, comment="是否删除")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")

    # 关系
    team_members = relationship("TeamMember", back_populates="team")
    owned_resources = relationship("Resource", back_populates="team_owner")

    # 索引
    __table_args__ = (
        Index('idx_team_creator', 'creator_ali_uid', 'creator_wy_id'),
    )


class TeamMember(Base):
    """团队成员模型"""
    __tablename__ = 'auth_team_members'

    id = Column(Integer, primary_key=True, autoincrement=True)
    team_id = Column(Integer, ForeignKey('auth_teams.id'), nullable=False, comment="团队ID")

    # 成员标识（使用 ali_uid + wy_id）
    member_ali_uid = Column(BigInteger, nullable=False, comment="成员阿里云ID")
    member_wy_id = Column(String(64), nullable=False, comment="成员网易ID")

    # 角色
    role = Column(String(32), default="member", comment="团队角色: owner, admin, member")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")

    # 关系
    team = relationship("Team", back_populates="team_members")

    # 索引
    __table_args__ = (
        Index('idx_team_member', 'team_id', 'member_ali_uid', 'member_wy_id', unique=True),
        Index('idx_member_user', 'member_ali_uid', 'member_wy_id'),
    )


class Resource(Base):
    """资源模型"""
    __tablename__ = 'auth_resources'

    id = Column(Integer, primary_key=True, autoincrement=True)
    resource_type = Column(String(32), nullable=False, comment="资源类型")
    resource_id = Column(String(64), nullable=False, comment="资源ID")
    resource_name = Column(String(256), nullable=True, comment="资源名称")

    # 个人所有者（使用 ali_uid + wy_id）
    owner_ali_uid = Column(BigInteger, nullable=True, comment="个人所有者阿里云ID")
    owner_wy_id = Column(String(64), nullable=True, comment="个人所有者网易ID")

    # 团队所有者
    team_owner_id = Column(Integer, ForeignKey('auth_teams.id'), nullable=True, comment="团队所有者ID")

    # 可见性
    is_public = Column(Boolean, default=False, comment="是否公开")
    is_deleted = Column(Boolean, default=False, comment="是否删除")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")

    # 关系
    team_owner = relationship("Team", back_populates="owned_resources")
    permissions = relationship("ResourcePermission", back_populates="resource")
    shares = relationship("Share", back_populates="resource")

    # 索引
    __table_args__ = (
        Index('idx_resource_type_id', 'resource_type', 'resource_id', unique=True),
        Index('idx_resource_owner', 'owner_ali_uid', 'owner_wy_id'),
        Index('idx_resource_team_owner', 'team_owner_id'),
    )


class Role(Base):
    """角色模型"""
    __tablename__ = 'auth_roles'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), nullable=False, unique=True, comment="角色名称")
    description = Column(Text, nullable=True, comment="角色描述")
    
    # 是否系统角色
    is_system = Column(Boolean, default=False, comment="是否系统角色")
    
    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")
    
    # 关系
    role_permissions = relationship("RolePermission", back_populates="role")
    user_roles = relationship("UserRole", back_populates="role")


class Permission(Base):
    """权限模型"""
    __tablename__ = 'auth_permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(64), nullable=False, unique=True, comment="权限名称")
    description = Column(Text, nullable=True, comment="权限描述")
    resource_type = Column(String(32), nullable=True, comment="适用的资源类型")
    
    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    
    # 关系
    role_permissions = relationship("RolePermission", back_populates="permission")
    resource_permissions = relationship("ResourcePermission", back_populates="permission")


class RolePermission(Base):
    """角色权限关联模型"""
    __tablename__ = 'auth_role_permissions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    role_id = Column(Integer, ForeignKey('auth_roles.id'), nullable=False, comment="角色ID")
    permission_id = Column(Integer, ForeignKey('auth_permissions.id'), nullable=False, comment="权限ID")
    
    # 关系
    role = relationship("Role", back_populates="role_permissions")
    permission = relationship("Permission", back_populates="role_permissions")
    
    # 索引
    __table_args__ = (
        Index('idx_role_permission', 'role_id', 'permission_id', unique=True),
    )


class UserRole(Base):
    """用户角色关联模型"""
    __tablename__ = 'auth_user_roles'

    id = Column(Integer, primary_key=True, autoincrement=True)

    # 用户标识（使用 ali_uid + wy_id）
    user_ali_uid = Column(BigInteger, nullable=False, comment="用户阿里云ID")
    user_wy_id = Column(String(64), nullable=False, comment="用户网易ID")

    role_id = Column(Integer, ForeignKey('auth_roles.id'), nullable=False, comment="角色ID")

    # 作用域（可选，用于限制角色的作用范围）
    scope_type = Column(String(32), nullable=True, comment="作用域类型")
    scope_id = Column(String(64), nullable=True, comment="作用域ID")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")

    # 关系
    role = relationship("Role", back_populates="user_roles")

    # 索引
    __table_args__ = (
        Index('idx_user_role', 'user_ali_uid', 'user_wy_id', 'role_id'),
    )


class ResourcePermission(Base):
    """资源权限模型（用于特定资源的权限授权）"""
    __tablename__ = 'auth_resource_permissions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    resource_id = Column(Integer, ForeignKey('auth_resources.id'), nullable=False, comment="资源ID")

    # 被授权用户（使用 ali_uid + wy_id）
    user_ali_uid = Column(BigInteger, nullable=True, comment="被授权用户阿里云ID")
    user_wy_id = Column(String(64), nullable=True, comment="被授权用户网易ID")

    # 被授权团队
    team_id = Column(Integer, ForeignKey('auth_teams.id'), nullable=True, comment="团队ID")

    permission_id = Column(Integer, ForeignKey('auth_permissions.id'), nullable=False, comment="权限ID")

    # 授权者（使用 ali_uid + wy_id）
    granted_by_ali_uid = Column(BigInteger, nullable=False, comment="授权者阿里云ID")
    granted_by_wy_id = Column(String(64), nullable=False, comment="授权者网易ID")

    # 过期时间
    expires_at = Column(DateTime, nullable=True, comment="过期时间")

    # 状态
    is_deleted = Column(Boolean, default=False, comment="是否删除")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")

    # 关系
    resource = relationship("Resource", back_populates="permissions")
    team = relationship("Team", foreign_keys=[team_id])
    permission = relationship("Permission", back_populates="resource_permissions")

    # 索引
    __table_args__ = (
        Index('idx_resource_user_permission', 'resource_id', 'user_ali_uid', 'user_wy_id', 'permission_id'),
        Index('idx_resource_team_permission', 'resource_id', 'team_id', 'permission_id'),
    )


class Share(Base):
    """分享模型"""
    __tablename__ = 'auth_shares'

    id = Column(Integer, primary_key=True, autoincrement=True)
    resource_id = Column(Integer, ForeignKey('auth_resources.id'), nullable=False, comment="资源ID")

    # 创建者（使用 ali_uid + wy_id）
    creator_ali_uid = Column(BigInteger, nullable=False, comment="创建者阿里云ID")
    creator_wy_id = Column(String(64), nullable=False, comment="创建者网易ID")

    # 分享信息
    share_code = Column(String(64), nullable=False, unique=True, comment="分享码")
    share_type = Column(String(32), nullable=False, comment="分享类型")
    password = Column(String(128), nullable=True, comment="访问密码")

    # 权限
    allowed_permissions = Column(Text, nullable=True, comment="允许的权限列表（JSON）")

    # 限制
    max_access_count = Column(Integer, nullable=True, comment="最大访问次数")
    current_access_count = Column(Integer, default=0, comment="当前访问次数")
    expires_at = Column(DateTime, nullable=True, comment="过期时间")

    # 状态
    is_active = Column(Boolean, default=True, comment="是否激活")

    # 时间戳
    gmt_created = Column(DateTime, default=get_current_time, comment="创建时间")
    gmt_modified = Column(DateTime, default=get_current_time, onupdate=get_current_time, comment="修改时间")

    # 关系
    resource = relationship("Resource", back_populates="shares")
    access_logs = relationship("ShareAccessLog", back_populates="share")

    # 索引
    __table_args__ = (
        Index('idx_share_code', 'share_code'),
        Index('idx_share_resource', 'resource_id'),
        Index('idx_share_creator', 'creator_ali_uid', 'creator_wy_id'),
    )


class ShareAccessLog(Base):
    """分享访问日志模型"""
    __tablename__ = 'auth_share_access_logs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    share_id = Column(Integer, ForeignKey('auth_shares.id'), nullable=False, comment="分享ID")

    # 访问者信息（使用 ali_uid + wy_id，如果已登录）
    accessor_ali_uid = Column(BigInteger, nullable=True, comment="访问者阿里云ID")
    accessor_wy_id = Column(String(64), nullable=True, comment="访问者网易ID")

    ip_address = Column(String(45), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")

    # 访问信息
    access_time = Column(DateTime, default=get_current_time, comment="访问时间")
    is_success = Column(Boolean, default=True, comment="是否访问成功")

    # 关系
    share = relationship("Share", back_populates="access_logs")
