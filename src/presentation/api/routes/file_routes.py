"""
文件和制品模块 API 路由
# ==================== 文件服务接口 ====================
"""
import asyncio
import traceback
from pathlib import Path
from fastapi import APIRouter, HTTPException, Request, Query, UploadFile, File, Form, Depends
from pydantic import BaseModel
from infrastructure.memory import memory_sdk
from loguru import logger
from src.presentation.api.dependencies.api_common_utils import get_request_id_dependency, handle_exception, package_api_result

from ....application.api_models import (
    PresignedUploadRequest, PresignedUploadResponse, ConfirmUploadRequest, ConfirmUploadResponse, 
    ResourceType, SessionResource
)
from ....application.file_api_models import (
    FileUploadResponse, FileStatusResponse, 
    SessionFilesRequest, SessionFilesResponse, 
    DownloadUrlsRequest, DownloadUrlsResponse,
    RenameFileRequest, RenameFileResponse,
    BatchDeleteFilesRequest, BatchDeleteFilesResponse,
    DeletedFile, FailedDeleteFile,
    ArtifactPreviewRequest, ArtifactPreviewResponse,
    ArtifactExistRequest, ArtifactExistResponse
)

from ....popclients.waiy_infra_client import WaiyInfraClient, WaiyInfraClientError
from ....domain.services.file_service import file_service, FileProcessError
from ....domain.services.auth_service import AuthContext, require_auth, require_file_read_permission
from ....presentation.api.dependencies.common_params import CommonParams, UniversalCommonParams
from ....infrastructure.database.models.file_models import FileType


# 添加状态常量
OK_STATUS = 200
FAIL_STATUS = 500


def _is_supported_file_format(filename: str) -> bool:
    """
    检查文件格式是否支持
    
    Args:
        filename: 文件名
        
    Returns:
        bool: 是否支持该文件格式
    """
    if not filename:
        return False
        
    # 提取文件扩展名（转为小写）
    ext = Path(filename).suffix.lower().lstrip('.')
    
    # 支持的文件格式列表
    supported_formats = {
        # 文档格式
        'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'xlsm',
        # 其他格式
        'md', 'markdown', 'html', 'htm', 'epub', 'mobi', 'rtf', 'txt',
        # 图片格式
        'jpg', 'jpeg', 'png', 'bmp', 'gif',
        # 音视频格式
        'mp4', 'mkv', 'avi', 'mov', 'wmv', 'mp3', 'wav', 'aac'
    }
    
    return ext in supported_formats


router = APIRouter(prefix="/api", tags=["file"])


@router.post("/files/get-upload-url")
async def create_presigned_upload(
    request: PresignedUploadRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    创建预签名上传链接（新接口）
    前端通过此接口获取上传链接，然后直接上传到OSS
    """
    try:
        logger.info(f"[API] 创建预签名上传: user={context.user_key}, file={request.file_info.file_name}")

        # 验证文件信息
        if not request.file_info.file_name:
            raise HTTPException(status_code=400, detail="文件名不能为空")

        if request.file_info.file_size <= 0:
            raise HTTPException(status_code=400, detail="文件大小必须大于0")

        # 验证文件大小（100M限制）
        max_size = 100 * 1024 * 1024
        if request.file_info.file_size > max_size:
            raise HTTPException(status_code=400, detail=f"文件大小超过限制，最大允许{max_size // (1024*1024)}MB")

        # 验证文件格式是否支持
        if not _is_supported_file_format(request.file_info.file_name):
            logger.warning(f"[API] 不支持的文件格式: {request.file_info.file_name}")
            return package_api_result(
                code="500",
                message="不支持的文件格式",
                data=None,
                request_id=request_id,
                success=False
            )

        # 如果没有提供session_id，会在service中自动生成
        session_id = request.session_id

        # 异步创建预签名上传
        file_obj, upload_url, expires_in, upload_headers = await file_service.create_presigned_upload(
            context=context,
            file_name=request.file_info.file_name,
            file_size=request.file_info.file_size,
            file_type=request.file_info.file_type,
            session_id=session_id,
            agent_id=request.agent_id,
            upload_file_type=request.file_type or "sessionFile"
        )

        response = PresignedUploadResponse(
            file_id=str(file_obj.id),
            session_id=str(file_obj.session_id),
            upload_url=upload_url,
            headers=upload_headers
        )

        logger.info(f"[API] 预签名上传创建成功: file_id={file_obj.id}, expires_in={expires_in}s")
        return package_api_result(
            code="200",
            data=response.model_dump(),
            message="预签名上传创建成功",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 创建预签名上传异常: {e}")
        raise HTTPException(status_code=500, detail="创建预签名上传失败")


@router.post("/files/confirm-upload")
async def confirm_presigned_upload(
    request: ConfirmUploadRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    确认预签名上传完成
    前端上传完成后调用此接口确认，触发后续处理
    """
    try:
        logger.info(f"[API] 确认预签名上传: user={context.user_key}, file_id={request.file_id}")

        # 验证文件ID
        try:
            file_id = int(request.file_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的文件ID")

        success = await file_service.confirm_presigned_upload(context=context,
            file_id=file_id,
            etag=request.etag)

        if not success:
            raise HTTPException(status_code=400, detail="确认上传失败，请检查文件是否已正确上传")

        logger.info(f"[API] 预签名上传确认成功: file_id={file_id}")

        response = ConfirmUploadResponse(
                    file_id=request.file_id,
                    status="completed",
                    message="上传确认成功，已处理完成"
        )

        return package_api_result(
            code="200",
            data=response.model_dump(),
            message="上传确认成功，已处理完成",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 确认预签名上传异常: {e}")
        return handle_exception(e, request_id)

@router.post("/files/list")
async def get_session_files(
    request: SessionFilesRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency),
):
    """
    获取某个会话下的所有文件
    支持按文件类型过滤
    """
    try:
        logger.info(
            f"[API] 获取会话文件: "
            f"session_id={request.session_id}, "
            f"artifact_types={request.artifact_types}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}",
            f"wy_id={context.wy_id}"
        )

        # 解析文件类型过滤
        file_types_list = None
        if request.artifact_types:
            file_types_list = [ft.strip() for ft in request.artifact_types if ft.strip()]
            # 验证文件类型
            valid_types = [FileType.ARTIFACT_FILE.value, FileType.SESSION_FILE.value]
            for ft in file_types_list:
                if ft not in valid_types:
                    raise HTTPException(status_code=400, detail=f"无效的文件类型: {ft}") 

        result = await asyncio.to_thread(
                file_service.get_session_files,
                owner_ali_uid=context.ali_uid,
                owner_wy_id=context.wy_id,
                session_id=request.session_id,
                file_types=file_types_list,
                max_result=request.max_results,
                next_token=request.next_token
            )
                
        if result.data:
            await asyncio.to_thread(
                file_service.set_files_kb_relationship,
                files=result.data,
                session_id=request.session_id,
                kb_id=request.kb_id
            )
        
        logger.info(f"[API] 获取会话文件成功: {result}")
        return package_api_result(
            code="200",
            data=result.data,
            message="获取会话文件成功",
            request_id=request_id,
            status=OK_STATUS,
            success=True,
            total_count=result.max_result,
            next_token=result.next_token
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 获取会话文件异常: {e}")
        return handle_exception(e, request_id)

@router.post("/files/download-urls")
async def get_download_urls(
    request: DownloadUrlsRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    根据文件ID列表获取下载链接
    批量获取多个文件的下载链接
    """
    try:
        logger.info(
            f"[API] 获取下载链接: "
            f"session_id={request.session_id}",
            f"artifact_ids={request.artifact_ids}, "
            f"expires={request.expires}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}",
            f"wy_id={context.wy_id}"
        )

        if not request.artifact_ids:
            raise HTTPException(status_code=400, detail="制品ID列表不能为空")

        if len(request.artifact_ids) > 100:
            raise HTTPException(status_code=400, detail="一次最多只能获取100个文件的下载链接")

        if request.expires < 60 or request.expires > 86400:  # 1分钟到1天
            raise HTTPException(status_code=400, detail="过期时间必须在60秒到86400秒之间")
        
        if not request.session_id:
            raise HTTPException(status_code=400, detail="会话ID不能为空")

        # 异步获取下载链接
        result = await asyncio.to_thread(
            file_service.get_download_urls_by_artifact_ids,
            request.session_id,
            request.artifact_ids, 
            request.file_format,
            request.expires
        )

        logger.info(f"[API] 获取下载链接成功: result={result}")
        return package_api_result(
            code="200",
            data=result,
            message="获取下载链接成功",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 获取下载链接异常: {e}")
        return handle_exception(e, request_id)


@router.post("/files/rename")
async def rename_file(
    request: RenameFileRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    重命名文件
    更新文件的显示名称
    """
    try:
        logger.info(
            f"[API] 重命名文件: "
            f"artifact_id={request.artifact_id}, "
            f"new_name={request.new_file_name}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}, "
            f"wy_id={context.wy_id}"
        )

        # 验证请求参数
        if not request.artifact_id or not request.artifact_id.strip():
            raise HTTPException(status_code=400, detail="制品ID不能为空")

        if not request.new_file_name or not request.new_file_name.strip():
            raise HTTPException(status_code=400, detail="新文件名不能为空")

        if len(request.new_file_name.strip()) > 255:
            raise HTTPException(status_code=400, detail="文件名长度不能超过255个字符")

        # 检查禁止字符
        forbidden_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        if any(char in request.new_file_name for char in forbidden_chars):
            raise HTTPException(
                status_code=400, 
                detail=f"文件名不能包含以下字符: {', '.join(forbidden_chars)}"
            )

        # 调用文件服务重命名
        rename_result = file_service.rename_file(
            context=context,
            artifact_id=request.artifact_id.strip(),
            new_file_name=request.new_file_name.strip()
        )

        if not rename_result:
            raise HTTPException(status_code=400, detail="重命名失败，请检查文件是否存在或您是否有权限")

        updated_file, old_file_name = rename_result

        # 构造响应
        response = RenameFileResponse(
            artifact_id=getattr(updated_file, 'artifact_id', '') or '',
            old_file_name=old_file_name,
            new_file_name=str(updated_file.title),
            gmt_modified=updated_file.gmt_modified.isoformat() if updated_file.gmt_modified is not None else ""
        )

        logger.info(f"[API] 文件重命名成功: artifact_id={request.artifact_id}, new_name={updated_file.title}")
        return package_api_result(
            code="200",
            data=response.model_dump(),
            message="文件重命名成功",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 重命名文件异常: {e}, stack={traceback.format_exc()}")
        return handle_exception(e, request_id)


@router.post("/files/batch-delete")
async def batch_delete_files(
    request: BatchDeleteFilesRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    批量删除文件
    删除多个文件的数据库记录、OSS文件和鉴权记录
    """
    try:
        logger.info(
            f"[API] 批量删除文件: "
            # f"session_id={request.session_id}, "
            f"artifact_ids={request.artifact_ids}, "
            f"delete_from_oss={request.delete_from_oss}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}, "
            f"wy_id={context.wy_id}"
        )

        # 验证请求参数
        if not request.artifact_ids:
            raise HTTPException(status_code=400, detail="制品ID列表不能为空")

        if len(request.artifact_ids) > 100:
            raise HTTPException(status_code=400, detail="一次最多只能删除100个文件")
        
        # if not request.session_id:
        #     raise HTTPException(status_code=400, detail="会话ID不能为空")

        # 验证制品ID格式
        for artifact_id in request.artifact_ids:
            if not artifact_id or not artifact_id.strip():
                raise HTTPException(status_code=400, detail=f"无效的制品ID: {artifact_id}")

        # 调用文件服务批量删除
        deleted_files, failed_files = file_service.batch_delete_files(
            context=context,
            artifact_ids=request.artifact_ids,
            delete_from_oss=request.delete_from_oss
        )

        # 构造响应
        response = BatchDeleteFilesResponse(
            deleted_files=[DeletedFile(**item) for item in deleted_files],
            failed_files=[FailedDeleteFile(**item) for item in failed_files],
            total_count=len(request.artifact_ids),
            deleted_count=len(deleted_files),
            failed_count=len(failed_files)
        )

        logger.info(
            f"[API] 批量删除文件完成: "
            f"总数={response.total_count}, "
            f"成功={response.deleted_count}, "
            f"失败={response.failed_count}"
        )

        return package_api_result(
            code="200",
            data=response.model_dump(),
            message=f"批量删除完成，成功{response.deleted_count}个，失败{response.failed_count}个",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 批量删除文件异常: {e}, stack={traceback.format_exc()}")
        return handle_exception(e, request_id)


@router.post("/files/get-artifact-preview")
async def get_artifact_preview(
    request: ArtifactPreviewRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    获取文件预览链接
    根据制品ID获取文件的预览链接
    """
    try:
        logger.info(
            f"[API] 获取文件预览链接: "
            f"artifact_id={request.artifact_id}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}, "
            f"wy_id={context.wy_id}"
        )

        # 验证请求参数
        if not request.artifact_id or not request.artifact_id.strip():
            raise HTTPException(status_code=400, detail="制品ID不能为空")

        # 调用文件服务获取文件链接（预览或下载）
        file_obj, url, url_type, expires_in = file_service.get_artifact_preview_url(
            context=context,
            artifact_id=request.artifact_id.strip()
        )
        
        # 构造响应
        response = ArtifactPreviewResponse(
            artifact_id=request.artifact_id,
            file_name=str(file_obj.title),
            url=url,
            url_type=url_type,
            expires_in=expires_in
        )

        # 根据链接类型确定返回消息
        message = "获取预览链接成功" if url_type == "preview" else "获取下载链接成功"
        
        logger.info(f"[API] 获取文件链接成功: artifact_id={request.artifact_id}, type={url_type}, expires_in={expires_in}s")
        return package_api_result(
            code="200",
            data=response.model_dump(),
            message=message,
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except FileProcessError as e:
        error_msg = str(e)
        logger.warning(f"[API] 文件处理异常: {error_msg}")
        
        # 根据错误消息内容返回合适的HTTP状态码
        if "文件不存在" in error_msg:
            status_code = 404
        elif "无权限访问" in error_msg:
            status_code = 403
        elif "未完成上传" in error_msg or "文件状态" in error_msg:
            status_code = 400
        else:
            status_code = 500
            
        return package_api_result(
            code=status_code,
            data=None,
            message=error_msg,
            request_id=request_id,
            status=status_code,
            success=False,
        )
    except Exception as e:
        logger.error(f"[API] 获取文件预览链接异常: {e}, stack={traceback.format_exc()}")
        return package_api_result(
            code=FAIL_STATUS,
            data=None,
            message="系统内部错误",
            request_id=request_id,
            status=FAIL_STATUS,
            success=False,
        )


@router.post("/files/is-artifact-existed")
async def is_artifact_existed(
    request: ArtifactExistRequest,
    context: AuthContext = Depends(require_auth),
    request_id: str = Depends(get_request_id_dependency)
):
    """
    检查文件是否存在
    根据制品ID检查文件是否存在且未被删除
    """
    try:
        logger.info(
            f"[API] 检查文件是否存在: "
            f"artifact_id={request.artifact_id}, "
            f"user_key={context.user_key}, "
            f"ali_uid={context.ali_uid}, "
            f"wy_id={context.wy_id}"
        )

        # 验证请求参数
        if not request.artifact_id or not request.artifact_id.strip():
            raise HTTPException(status_code=400, detail="制品ID不能为空")

        # 调用文件服务检查文件是否存在
        exists = file_service.is_artifact_existed(
            artifact_id=request.artifact_id.strip()
        )

        # 构造响应
        response = ArtifactExistResponse(
            artifact_id=request.artifact_id,
            exists=exists
        )

        logger.info(f"[API] 文件存在性检查完成: artifact_id={request.artifact_id}, exists={exists}")
        return package_api_result(
            code="200",
            data=response.model_dump(),
            message="文件存在性检查完成",
            request_id=request_id,
            status=OK_STATUS,
            success=True
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[API] 检查文件是否存在异常: {e}, stack={traceback.format_exc()}")
        return handle_exception(e, request_id)


