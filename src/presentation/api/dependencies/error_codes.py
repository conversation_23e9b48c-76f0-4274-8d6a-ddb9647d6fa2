"""
错误码定义模块
统一管理所有API错误码，从410开始编号
"""

from enum import Enum
from typing import Dict, Any


class ErrorCode(Enum):
    """错误码枚举类，提供类型安全的错误码定义"""
    OK = ("200", "成功", 200)

    # 客户端错误 (410-499)
    PARAM_NOT_NULL = ("PARAM_NOT_NULL", "参数不能为空", 410)
    PARAM_NOT_EMPTY = ("PARAM_NOT_EMPTY", "参数不能为空字符串或空列表", 411)
    PARAM_INVALID = ("PARAM_INVALID", "参数无效", 412)
    PARAM_ERROR = ("PARAM_ERROR", "参数错误", 413)
    OBJECT_NOT_FOUND = ("OBJECT_NOT_FOUND", "对象不存在", 414)
    OBJECT_ALREADY_EXISTS = ("OBJECT_ALREADY_EXISTS", "对象已存在", 415)
    PERMISSION_DENIED = ("PERMISSION_DENIED", "权限不足", 416)
    OBJECT_DUPLICATE = ("OBJECT_DUPLICATE", "对象重复", 417)
    OBJECT_STATE_ERROR = ("OBJECT_STATE_ERROR", "对象状态错误", 418)
    OBJECT_QUOTA_LIMIT = ("OBJECT_QUOTA_LIMIT", "对象操作限额", 419)
    OPERATION_NOT_SUPPORTED = ("OPERATION_NOT_SUPPORTED", "操作不支持", 420)

    # 文件处理错误 (430-449)
    FILE_PROCESSING_FAILED = ("FILE_PROCESSING_FAILED", "文件处理失败", 430)
    FILE_PROCESSING_TIMEOUT = ("FILE_PROCESSING_TIMEOUT", "文件处理超时", 431)
    FILE_CONTENT_TOO_LARGE = ("FILE_CONTENT_TOO_LARGE", "文件内容过大", 432)

    # 系统错误 (500-599)
    SYSTEM_ERROR = ("SYSTEM_ERROR", "系统错误", 500)
    INTERNAL_SERVER_ERROR = ("INTERNAL_SERVER_ERROR", "内部服务器错误", 501)
    SERVICE_UNAVAILABLE = ("SERVICE_UNAVAILABLE", "服务不可用", 502)
    DATABASE_ERROR = ("DATABASE_ERROR", "数据库错误", 503)
    NETWORK_ERROR = ("NETWORK_ERROR", "网络错误", 504)
    TIMEOUT_ERROR = ("TIMEOUT_ERROR", "超时错误", 505)

    def __init__(self, key: str, message: str, code: int):
        self.key = key
        self.message = message
        self.code = code

    @classmethod
    def get_by_key(cls, key: str) -> 'ErrorCode':
        """根据key获取错误码枚举"""
        for error_code in cls:
            if error_code.key == key:
                return error_code
        return cls.SYSTEM_ERROR

    @classmethod
    def get_by_code(cls, code: int) -> 'ErrorCode':
        """根据数字错误码获取错误码枚举"""
        for error_code in cls:
            if error_code.code == code:
                return error_code
        return cls.SYSTEM_ERROR


# 为了向后兼容，保留ERROR_CODES字典
ERROR_CODES: Dict[str, Dict[str, Any]] = {
    error_code.key: {
        "message": error_code.message,
        "code": error_code.code
    }
    for error_code in ErrorCode
}


def get_error_code(error_key: str) -> int:
    """
    获取错误码对应的数字
    
    Args:
        error_key: 错误码字符串
        
    Returns:
        int: 对应的数字错误码，如果不存在则返回500
    """
    return ErrorCode.get_by_key(error_key).code


def get_error_message(error_key: str) -> str:
    """
    获取错误码对应的描述信息
    
    Args:
        error_key: 错误码字符串
        
    Returns:
        str: 对应的错误描述，如果不存在则返回"系统错误"
    """
    return ErrorCode.get_by_key(error_key).message


def get_error_key(error_code: int) -> str:
    """
    根据数字错误码获取错误码字符串
    
    Args:
        error_code: 数字错误码
        
    Returns:
        str: 对应的错误码字符串，如果不存在则返回"SYSTEM_ERROR"
    """
    return ErrorCode.get_by_code(error_code).key


def is_client_error(error_code: int) -> bool:
    """
    判断是否为客户端错误
    
    Args:
        error_code: 数字错误码
        
    Returns:
        bool: 如果是客户端错误返回True，否则返回False
    """
    return 410 <= error_code <= 499


def is_server_error(error_code: int) -> bool:
    """
    判断是否为服务器错误
    
    Args:
        error_code: 数字错误码
        
    Returns:
        bool: 如果是服务器错误返回True，否则返回False
    """
    return error_code >= 500


def get_error_category(error_code: int) -> str:
    """
    获取错误码分类
    
    Args:
        error_code: 数字错误码
        
    Returns:
        str: 错误分类 ("client_error", "server_error", "unknown")
    """
    if is_client_error(error_code):
        return "client_error"
    elif is_server_error(error_code):
        return "server_error"
    else:
        return "unknown"


def validate_error_key(error_key: str) -> bool:
    """
    验证错误码字符串是否有效
    
    Args:
        error_key: 错误码字符串
        
    Returns:
        bool: 如果错误码有效返回True，否则返回False
    """
    try:
        ErrorCode.get_by_key(error_key)
        return True
    except:
        return False


def get_all_error_keys() -> list:
    """
    获取所有错误码字符串列表
    
    Returns:
        list: 所有错误码字符串的列表
    """
    return [error_code.key for error_code in ErrorCode]
