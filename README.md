# Alpha Service

## 🚀 快速开始

### 系统要求

- Python 3.9+
- pip (Python 包管理器)
- Git

### 1. 克隆项目

```bash
git clone <repository-url>
cd alpha-service
```

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate

```

### 3. 安装依赖

```bash
# 升级 pip
pip install --upgrade pip

# 第一步：安装pyproject.toml中的依赖
pip install -e . -i http://yum.tbsite.net/aliyun-pypi/simple/ --trusted-host yum.tbsite.net --extra-index-url https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com

# 第二步：安装本地 popclients 依赖（开发阶段必需，部分pop sdk没有上传pip）
cd src/popclients/eds-user-inner-20210316 && pip install . && cd -
cd src/popclients/wuyingaiinner-20250718 && pip install . && cd -
cd src/popclients/eds-storage-inner-20230117 && pip install . && cd -
```

### 4. 配置环境

```bash
# 配置文件，类似java profile，在如下文件中增加配置项
cat properties.toml

```

### 5. 启动服务

```bash
# 启动开发服务器
python start_service.py
```

服务启动后，访问：

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/
- **API 根路径**: http://localhost:8000/api/

## 📋 依赖说明

### 完整依赖列表

查看 `pyproject.toml` 文件的 `[project.dependencies]` 部分获取完整的依赖列表。

## 📁 项目结构

```
alpha-service/
├── src/                    # 源代码
│   ├── application/        # 应用层（API 模型）
│   ├── domain/            # 领域层（业务逻辑）
│   ├── infrastructure/    # 基础设施层（数据库、外部服务）
│   ├── presentation/      # 表现层（API 路由）
│   └── shared/           # 共享工具
├── conf/                  # 配置文件
├── logs/                  # 日志文件
├── static/               # 静态文件
├── tests/                # 测试文件
├── doc/                  # 文档
├── venv/                 # 虚拟环境
├── pyproject.toml        # 项目配置和依赖
├── start_service.py      # 服务启动脚本
└── README.md            # 项目说明
```

### 要实现一个新功能，需要在以下位置编写代码：

#### 1. 📊 **数据模型层** (Infrastructure Layer)

**位置**: `src/infrastructure/database/`

```
src/infrastructure/database/
├── models/                # 数据库模型
│   ├── session_models.py  # 会话相关模型
│   ├── file_models.py     # 文件相关模型
│   └── auth_models.py     # 认证相关模型
└── repositories/          # 数据访问层
    ├── session_repository.py  # 会话数据访问
    ├── file_repository.py     # 文件数据访问
    └── auth_repository.py     # 认证数据访问
```

**作用**: 定义数据库表结构和数据访问逻辑

#### 2. 🏢 **业务服务层** (Domain Layer)

**位置**: `src/domain/services/`

```
src/domain/services/
├── session_service.py     # 会话业务逻辑
├── file_service.py        # 文件业务逻辑
├── auth_service.py        # 认证业务逻辑
├── session_manager.py     # 会话管理器
└── message_processor.py   # 消息处理器
```

**作用**: 实现核心业务逻辑，不依赖外部框架

#### 3. 📝 **API 模型层** (Application Layer)

**位置**: `src/application/api_models.py`

```python
# 定义请求和响应模型
class NewFeatureRequest(BaseModel):
    param1: str = Field(..., description="参数1")
    param2: int = Field(..., description="参数2")

class NewFeatureResponse(BaseModel):
    code: int = Field(..., description="响应码")
    msg: str = Field(..., description="响应消息")
    data: Dict[str, Any] = Field(..., description="响应数据")
```

#### 4. 🌐 **API 路由层** (Presentation Layer)

**位置**: `src/presentation/api/routes/`

```
src/presentation/api/routes/
├── pythonic_routes.py     # API路由
└── xxx_routes.py        # 其他路由
```

**作用**: 定义 HTTP 接口，处理请求响应
